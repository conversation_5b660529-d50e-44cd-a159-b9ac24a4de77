package com.appystore.mrecharge.util;

import android.Manifest;
import android.app.Activity;
import android.app.NotificationManager;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.provider.Settings;
import android.util.Log;

import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import java.util.ArrayList;
import java.util.List;

/**
 * Helper class for managing permissions in Android 15
 */
public class PermissionHelper {
    private static final String TAG = "PermissionHelper";
    
    // Permission request codes
    public static final int REQUEST_CODE_BASIC_PERMISSIONS = 1001;
    public static final int REQUEST_CODE_NOTIFICATION_PERMISSION = 1002;
    public static final int REQUEST_CODE_EXACT_ALARM_PERMISSION = 1003;
    public static final int REQUEST_CODE_SYSTEM_ALERT_WINDOW = 1004;

    // Basic permissions required by the app
    private static final String[] BASIC_PERMISSIONS = {
        Manifest.permission.SEND_SMS,
        Manifest.permission.RECEIVE_SMS,
        Manifest.permission.READ_SMS,
        Manifest.permission.CALL_PHONE,
        Manifest.permission.READ_PHONE_STATE,
        Manifest.permission.RECEIVE_BOOT_COMPLETED,
        Manifest.permission.WAKE_LOCK,
        Manifest.permission.ACCESS_NETWORK_STATE,
        Manifest.permission.INTERNET,
        Manifest.permission.ACCESS_WIFI_STATE,
        Manifest.permission.FOREGROUND_SERVICE
    };

    // Android 15+ foreground service permissions
    private static final String[] FOREGROUND_SERVICE_PERMISSIONS = {
        "android.permission.FOREGROUND_SERVICE_DATA_SYNC"
    };

    /**
     * Check if all basic permissions are granted
     * @param context The context to check permissions
     * @return true if all permissions are granted
     */
    public static boolean hasAllBasicPermissions(Context context) {
        for (String permission : BASIC_PERMISSIONS) {
            if (ContextCompat.checkSelfPermission(context, permission) != PackageManager.PERMISSION_GRANTED) {
                return false;
            }
        }
        return true;
    }

    /**
     * Check if all foreground service permissions are granted (Android 15+)
     * @param context The context to check permissions
     * @return true if all foreground service permissions are granted
     */
    public static boolean hasAllForegroundServicePermissions(Context context) {
        if (Build.VERSION.SDK_INT >= 35) { // Android 15
            for (String permission : FOREGROUND_SERVICE_PERMISSIONS) {
                if (ContextCompat.checkSelfPermission(context, permission) != PackageManager.PERMISSION_GRANTED) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * Get list of missing basic permissions
     * @param context The context to check permissions
     * @return List of missing permissions
     */
    public static List<String> getMissingBasicPermissions(Context context) {
        List<String> missingPermissions = new ArrayList<>();
        for (String permission : BASIC_PERMISSIONS) {
            if (ContextCompat.checkSelfPermission(context, permission) != PackageManager.PERMISSION_GRANTED) {
                missingPermissions.add(permission);
            }
        }

        // Add foreground service permissions for Android 15+
        if (Build.VERSION.SDK_INT >= 35) {
            for (String permission : FOREGROUND_SERVICE_PERMISSIONS) {
                if (ContextCompat.checkSelfPermission(context, permission) != PackageManager.PERMISSION_GRANTED) {
                    missingPermissions.add(permission);
                }
            }
        }

        return missingPermissions;
    }

    /**
     * Request basic permissions
     * @param activity The activity to request permissions from
     */
    public static void requestBasicPermissions(Activity activity) {
        List<String> missingPermissions = getMissingBasicPermissions(activity);
        if (!missingPermissions.isEmpty()) {
            ActivityCompat.requestPermissions(activity, 
                missingPermissions.toArray(new String[0]), 
                REQUEST_CODE_BASIC_PERMISSIONS);
        }
    }

    /**
     * Check if notification permission is granted (Android 13+)
     * @param context The context to check
     * @return true if notification permission is granted
     */
    public static boolean hasNotificationPermission(Context context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            return ContextCompat.checkSelfPermission(context, Manifest.permission.POST_NOTIFICATIONS) 
                == PackageManager.PERMISSION_GRANTED;
        }
        return true; // Not required for older versions
    }

    /**
     * Request notification permission (Android 13+)
     * @param activity The activity to request permission from
     */
    public static void requestNotificationPermission(Activity activity) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (!hasNotificationPermission(activity)) {
                ActivityCompat.requestPermissions(activity, 
                    new String[]{Manifest.permission.POST_NOTIFICATIONS}, 
                    REQUEST_CODE_NOTIFICATION_PERMISSION);
            }
        }
    }

    /**
     * Check if exact alarm permission is granted (Android 12+)
     * @param context The context to check
     * @return true if exact alarm permission is granted
     */
    public static boolean hasExactAlarmPermission(Context context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            return Settings.canDrawOverlays(context); // This is a placeholder - actual implementation may vary
        }
        return true; // Not required for older versions
    }

    /**
     * Request exact alarm permission (Android 12+)
     * @param activity The activity to request permission from
     */
    public static void requestExactAlarmPermission(Activity activity) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            try {
                Intent intent = new Intent(Settings.ACTION_REQUEST_SCHEDULE_EXACT_ALARM);
                intent.setData(Uri.parse("package:" + activity.getPackageName()));
                activity.startActivityForResult(intent, REQUEST_CODE_EXACT_ALARM_PERMISSION);
            } catch (Exception e) {
                Log.e(TAG, "Error requesting exact alarm permission: " + e.getMessage(), e);
                Android15CompatibilityHelper.recordException(e);
            }
        }
    }

    /**
     * Check if system alert window permission is granted
     * @param context The context to check
     * @return true if system alert window permission is granted
     */
    public static boolean hasSystemAlertWindowPermission(Context context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            return Settings.canDrawOverlays(context);
        }
        return true; // Not required for older versions
    }

    /**
     * Request system alert window permission
     * @param activity The activity to request permission from
     */
    public static void requestSystemAlertWindowPermission(Activity activity) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (!hasSystemAlertWindowPermission(activity)) {
                try {
                    Intent intent = new Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION);
                    intent.setData(Uri.parse("package:" + activity.getPackageName()));
                    activity.startActivityForResult(intent, REQUEST_CODE_SYSTEM_ALERT_WINDOW);
                } catch (Exception e) {
                    Log.e(TAG, "Error requesting system alert window permission: " + e.getMessage(), e);
                    Android15CompatibilityHelper.recordException(e);
                }
            }
        }
    }

    /**
     * Request all necessary permissions for Android 15
     * @param activity The activity to request permissions from
     */
    public static void requestAllNecessaryPermissions(Activity activity) {
        try {
            // Request basic permissions first
            requestBasicPermissions(activity);
            
            // Request notification permission (Android 13+)
            requestNotificationPermission(activity);
            
            // Request exact alarm permission (Android 12+)
            requestExactAlarmPermission(activity);
            
            // Request system alert window permission
            requestSystemAlertWindowPermission(activity);
            
        } catch (Exception e) {
            Log.e(TAG, "Error requesting permissions: " + e.getMessage(), e);
            Android15CompatibilityHelper.recordException(e);
        }
    }

    /**
     * Check if all necessary permissions are granted
     * @param context The context to check
     * @return true if all necessary permissions are granted
     */
    public static boolean hasAllNecessaryPermissions(Context context) {
        return hasAllBasicPermissions(context) &&
               hasAllForegroundServicePermissions(context) &&
               hasNotificationPermission(context) &&
               hasExactAlarmPermission(context) &&
               hasSystemAlertWindowPermission(context);
    }

    /**
     * Get a user-friendly description of missing permissions
     * @param context The context to check
     * @return String describing missing permissions
     */
    public static String getMissingPermissionsDescription(Context context) {
        StringBuilder description = new StringBuilder();
        
        if (!hasAllBasicPermissions(context)) {
            description.append("Basic app permissions (SMS, Phone, etc.)\n");
        }
        
        if (!hasNotificationPermission(context)) {
            description.append("Notification permission\n");
        }
        
        if (!hasExactAlarmPermission(context)) {
            description.append("Exact alarm permission\n");
        }
        
        if (!hasSystemAlertWindowPermission(context)) {
            description.append("System alert window permission\n");
        }
        
        return description.toString().trim();
    }
}
