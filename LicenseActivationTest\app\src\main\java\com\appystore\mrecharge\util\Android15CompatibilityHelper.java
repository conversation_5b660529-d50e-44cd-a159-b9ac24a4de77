package com.appystore.mrecharge.util;

import android.app.Activity;
import android.app.AlertDialog;
import android.content.Context;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.widget.Toast;

import com.google.firebase.crashlytics.FirebaseCrashlytics;

/**
 * Helper class for Android 15 compatibility and safe UI operations
 */
public class Android15CompatibilityHelper {
    private static final String TAG = "Android15Helper";

    /**
     * Check if an activity is safe to use for UI operations
     * @param activity The activity to check
     * @return true if the activity is safe to use
     */
    public static boolean isActivitySafe(Activity activity) {
        if (activity == null) {
            return false;
        }
        
        if (activity.isFinishing()) {
            return false;
        }
        
        // Check isDestroyed() only for API 17+
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
            return !activity.isDestroyed();
        }
        
        return true;
    }

    /**
     * Show a Toast message safely
     * @param context The context to use
     * @param message The message to display
     * @param duration Toast duration (Toast.LENGTH_SHORT or Toast.LENGTH_LONG)
     */
    public static void showSafeToast(Context context, String message, int duration) {
        if (context == null || message == null) {
            Log.w(TAG, "Cannot show toast - context or message is null");
            return;
        }

        try {
            // Ensure we're on the main thread
            if (Looper.myLooper() == Looper.getMainLooper()) {
                Toast.makeText(context, message, duration).show();
            } else {
                new Handler(Looper.getMainLooper()).post(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            Toast.makeText(context, message, duration).show();
                        } catch (Exception e) {
                            Log.e(TAG, "Error showing toast on main thread: " + e.getMessage(), e);
                        }
                    }
                });
            }
        } catch (Exception e) {
            Log.e(TAG, "Error showing toast: " + e.getMessage(), e);
            recordException(e);
        }
    }

    /**
     * Show an AlertDialog safely
     * @param activity The activity to use
     * @param title Dialog title
     * @param message Dialog message
     * @param positiveButtonText Positive button text (can be null)
     * @param positiveButtonListener Positive button listener (can be null)
     */
    public static void showSafeDialog(Activity activity, String title, String message, 
                                    String positiveButtonText, Runnable positiveButtonListener) {
        if (!isActivitySafe(activity)) {
            Log.w(TAG, "Activity is not safe for dialog display");
            return;
        }

        try {
            // Ensure we're on the main thread
            if (Looper.myLooper() == Looper.getMainLooper()) {
                createAndShowDialog(activity, title, message, positiveButtonText, positiveButtonListener);
            } else {
                activity.runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        createAndShowDialog(activity, title, message, positiveButtonText, positiveButtonListener);
                    }
                });
            }
        } catch (Exception e) {
            Log.e(TAG, "Error showing dialog: " + e.getMessage(), e);
            recordException(e);
        }
    }

    private static void createAndShowDialog(Activity activity, String title, String message,
                                          String positiveButtonText, Runnable positiveButtonListener) {
        try {
            if (!isActivitySafe(activity)) {
                return;
            }

            AlertDialog.Builder builder = new AlertDialog.Builder(activity);
            builder.setTitle(title);
            builder.setMessage(message);
            
            if (positiveButtonText != null) {
                builder.setPositiveButton(positiveButtonText, (dialog, which) -> {
                    if (positiveButtonListener != null) {
                        try {
                            positiveButtonListener.run();
                        } catch (Exception e) {
                            Log.e(TAG, "Error in positive button listener: " + e.getMessage(), e);
                            recordException(e);
                        }
                    }
                });
            } else {
                builder.setPositiveButton("OK", null);
            }

            AlertDialog dialog = builder.create();
            dialog.show();
        } catch (Exception e) {
            Log.e(TAG, "Error creating dialog: " + e.getMessage(), e);
            recordException(e);
        }
    }

    /**
     * Run a task on the UI thread safely
     * @param activity The activity to use
     * @param task The task to run
     */
    public static void runOnUiThreadSafely(Activity activity, Runnable task) {
        if (!isActivitySafe(activity) || task == null) {
            Log.w(TAG, "Cannot run task - activity is not safe or task is null");
            return;
        }

        try {
            if (Looper.myLooper() == Looper.getMainLooper()) {
                task.run();
            } else {
                activity.runOnUiThread(task);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error running task on UI thread: " + e.getMessage(), e);
            recordException(e);
        }
    }

    /**
     * Record an exception to Firebase Crashlytics safely
     * @param exception The exception to record
     */
    public static void recordException(Exception exception) {
        try {
            FirebaseCrashlytics.getInstance().recordException(exception);
        } catch (Exception e) {
            Log.e(TAG, "Error recording exception to Crashlytics: " + e.getMessage(), e);
        }
    }

    /**
     * Log a message to Firebase Crashlytics safely
     * @param message The message to log
     */
    public static void logToCrashlytics(String message) {
        try {
            FirebaseCrashlytics.getInstance().log(message);
        } catch (Exception e) {
            Log.e(TAG, "Error logging to Crashlytics: " + e.getMessage(), e);
        }
    }

    /**
     * Check if the current Android version is 15 or higher
     * @return true if running on Android 15+
     */
    public static boolean isAndroid15OrHigher() {
        return Build.VERSION.SDK_INT >= 35; // Android 15 is API level 35
    }

    /**
     * Get a safe string representation of an object
     * @param obj The object to convert to string
     * @return String representation or "null" if object is null
     */
    public static String safeToString(Object obj) {
        return obj != null ? obj.toString() : "null";
    }
}
