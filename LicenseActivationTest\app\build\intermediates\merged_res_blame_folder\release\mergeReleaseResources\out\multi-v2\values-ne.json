{"logs": [{"outputFile": "com.appystore.mrecharge.app-mergeReleaseResources-53:/values-ne/values-ne.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\812f317bcdb04d011f659e61565a4ab5\\transformed\\play-services-base-18.5.0\\res\\values-ne\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,454,584,697,864,996,1102,1203,1379,1489,1649,1778,1922,2070,2132,2200", "endColumns": "106,153,129,112,166,131,105,100,175,109,159,128,143,147,61,67,87", "endOffsets": "299,453,583,696,863,995,1101,1202,1378,1488,1648,1777,1921,2069,2131,2199,2287"}, "to": {"startLines": "50,51,52,53,54,55,56,57,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4764,4875,5033,5167,5284,5455,5591,5701,5970,6150,6264,6428,6561,6709,6861,6927,6999", "endColumns": "110,157,133,116,170,135,109,104,179,113,163,132,147,151,65,71,91", "endOffsets": "4870,5028,5162,5279,5450,5586,5696,5801,6145,6259,6423,6556,6704,6856,6922,6994,7086"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\f4e40cb66b2591bc7f67ef95d552f790\\transformed\\browser-1.4.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,274,388", "endColumns": "106,111,113,107", "endOffsets": "157,269,383,491"}, "to": {"startLines": "68,72,73,74", "startColumns": "4,4,4,4", "startOffsets": "7091,7420,7532,7646", "endColumns": "106,111,113,107", "endOffsets": "7193,7527,7641,7749"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\b4c8334a260b71f191b97485b06ad1d4\\transformed\\material-1.12.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,262,351,439,521,616,705,807,917,1004,1064,1130,1226,1292,1353,1458,1522,1594,1652,1726,1788,1842,1955,2015,2076,2135,2213,2337,2418,2500,2600,2685,2770,2906,2987,3070,3201,3284,3370,3432,3486,3552,3629,3708,3779,3862,3931,4007,4088,4156,4260,4351,4429,4522,4619,4693,4772,4870,4930,5018,5084,5172,5260,5322,5390,5453,5519,5624,5730,5825,5930,5996,6054,6138,6227,6303", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,88,87,81,94,88,101,109,86,59,65,95,65,60,104,63,71,57,73,61,53,112,59,60,58,77,123,80,81,99,84,84,135,80,82,130,82,85,61,53,65,76,78,70,82,68,75,80,67,103,90,77,92,96,73,78,97,59,87,65,87,87,61,67,62,65,104,105,94,104,65,57,83,88,75,72", "endOffsets": "257,346,434,516,611,700,802,912,999,1059,1125,1221,1287,1348,1453,1517,1589,1647,1721,1783,1837,1950,2010,2071,2130,2208,2332,2413,2495,2595,2680,2765,2901,2982,3065,3196,3279,3365,3427,3481,3547,3624,3703,3774,3857,3926,4002,4083,4151,4255,4346,4424,4517,4614,4688,4767,4865,4925,5013,5079,5167,5255,5317,5385,5448,5514,5619,5725,5820,5925,5991,6049,6133,6222,6298,6371"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,69,70,71,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,137,138,139", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3302,3391,3479,3561,3656,4465,4567,4677,7198,7258,7324,7754,7820,7881,7986,8050,8122,8180,8254,8316,8370,8483,8543,8604,8663,8741,8865,8946,9028,9128,9213,9298,9434,9515,9598,9729,9812,9898,9960,10014,10080,10157,10236,10307,10390,10459,10535,10616,10684,10788,10879,10957,11050,11147,11221,11300,11398,11458,11546,11612,11700,11788,11850,11918,11981,12047,12152,12258,12353,12458,12524,12582,12746,12835,12911", "endLines": "5,35,36,37,38,39,47,48,49,69,70,71,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,137,138,139", "endColumns": "12,88,87,81,94,88,101,109,86,59,65,95,65,60,104,63,71,57,73,61,53,112,59,60,58,77,123,80,81,99,84,84,135,80,82,130,82,85,61,53,65,76,78,70,82,68,75,80,67,103,90,77,92,96,73,78,97,59,87,65,87,87,61,67,62,65,104,105,94,104,65,57,83,88,75,72", "endOffsets": "307,3386,3474,3556,3651,3740,4562,4672,4759,7253,7319,7415,7815,7876,7981,8045,8117,8175,8249,8311,8365,8478,8538,8599,8658,8736,8860,8941,9023,9123,9208,9293,9429,9510,9593,9724,9807,9893,9955,10009,10075,10152,10231,10302,10385,10454,10530,10611,10679,10783,10874,10952,11045,11142,11216,11295,11393,11453,11541,11607,11695,11783,11845,11913,11976,12042,12147,12253,12348,12453,12519,12577,12661,12830,12906,12979"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\b42671bce86d044f55bdf4e61e8361bd\\transformed\\appcompat-1.7.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,325,433,524,631,751,835,914,1005,1098,1193,1287,1387,1480,1575,1669,1760,1851,1937,2050,2151,2254,2367,2477,2594,2761,2872", "endColumns": "108,110,107,90,106,119,83,78,90,92,94,93,99,92,94,93,90,90,85,112,100,102,112,109,116,166,110,79", "endOffsets": "209,320,428,519,626,746,830,909,1000,1093,1188,1282,1382,1475,1570,1664,1755,1846,1932,2045,2146,2249,2362,2472,2589,2756,2867,2947"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,136", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "312,421,532,640,731,838,958,1042,1121,1212,1305,1400,1494,1594,1687,1782,1876,1967,2058,2144,2257,2358,2461,2574,2684,2801,2968,12666", "endColumns": "108,110,107,90,106,119,83,78,90,92,94,93,99,92,94,93,90,90,85,112,100,102,112,109,116,166,110,79", "endOffsets": "416,527,635,726,833,953,1037,1116,1207,1300,1395,1489,1589,1682,1777,1871,1962,2053,2139,2252,2353,2456,2569,2679,2796,2963,3074,12741"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\a8ddf21c5cfcfff775adf0ab8e498f49\\transformed\\credentials-1.2.0-rc01\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,166", "endColumns": "110,111", "endOffsets": "161,273"}, "to": {"startLines": "33,34", "startColumns": "4,4", "startOffsets": "3079,3190", "endColumns": "110,111", "endOffsets": "3185,3297"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\42a8a4ad246f20dee6506de075e12431\\transformed\\play-services-basement-18.5.0\\res\\values-ne\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "159", "endOffsets": "354"}, "to": {"startLines": "58", "startColumns": "4", "startOffsets": "5806", "endColumns": "163", "endOffsets": "5965"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\561943d2b2d87f776fb7a0e02865f59c\\transformed\\core-1.13.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,261,363,469,567,667,775", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "153,256,358,464,562,662,770,871"}, "to": {"startLines": "40,41,42,43,44,45,46,140", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3745,3848,3951,4053,4159,4257,4357,12984", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "3843,3946,4048,4154,4252,4352,4460,13080"}}]}]}