[{"merged": "com.appystore.mrecharge.app-debug-55:/drawable_rightorder.xml.flat", "source": "com.appystore.mrecharge.app-main-57:/drawable/rightorder.xml"}, {"merged": "com.appystore.mrecharge.app-debug-55:/menu_menu_main.xml.flat", "source": "com.appystore.mrecharge.app-main-57:/menu/menu_main.xml"}, {"merged": "com.appystore.mrecharge.app-debug-55:/drawable_nav_item_background.xml.flat", "source": "com.appystore.mrecharge.app-main-57:/drawable/nav_item_background.xml"}, {"merged": "com.appystore.mrecharge.app-debug-55:/menu_bottom_navigation_menu.xml.flat", "source": "com.appystore.mrecharge.app-main-57:/menu/bottom_navigation_menu.xml"}, {"merged": "com.appystore.mrecharge.app-debug-55:/drawable_footer_background.xml.flat", "source": "com.appystore.mrecharge.app-main-57:/drawable/footer_background.xml"}, {"merged": "com.appystore.mrecharge.app-debug-55:/drawable_ic_home_black_24dp.xml.flat", "source": "com.appystore.mrecharge.app-main-57:/drawable/ic_home_black_24dp.xml"}, {"merged": "com.appystore.mrecharge.app-debug-55:/drawable_modern_toggle_off.xml.flat", "source": "com.appystore.mrecharge.app-main-57:/drawable/modern_toggle_off.xml"}, {"merged": "com.appystore.mrecharge.app-debug-55:/layout_custom_dialog.xml.flat", "source": "com.appystore.mrecharge.app-main-57:/layout/custom_dialog.xml"}, {"merged": "com.appystore.mrecharge.app-debug-55:/mipmap-xxhdpi_ic_launcher.png.flat", "source": "com.appystore.mrecharge.app-main-57:/mipmap-xxhdpi/ic_launcher.png"}, {"merged": "com.appystore.mrecharge.app-debug-55:/drawable_toggle_off.png.flat", "source": "com.appystore.mrecharge.app-main-57:/drawable/toggle_off.png"}, {"merged": "com.appystore.mrecharge.app-debug-55:/layout_listmain.xml.flat", "source": "com.appystore.mrecharge.app-main-57:/layout/listmain.xml"}, {"merged": "com.appystore.mrecharge.app-debug-55:/xml_backup_rules.xml.flat", "source": "com.appystore.mrecharge.app-main-57:/xml/backup_rules.xml"}, {"merged": "com.appystore.mrecharge.app-debug-55:/drawable_card_background.xml.flat", "source": "com.appystore.mrecharge.app-main-57:/drawable/card_background.xml"}, {"merged": "com.appystore.mrecharge.app-debug-55:/drawable_toggle_selector.xml.flat", "source": "com.appystore.mrecharge.app-main-57:/drawable/toggle_selector.xml"}, {"merged": "com.appystore.mrecharge.app-debug-55:/drawable_save.xml.flat", "source": "com.appystore.mrecharge.app-main-57:/drawable/save.xml"}, {"merged": "com.appystore.mrecharge.app-debug-55:/drawable_rounded_corner.xml.flat", "source": "com.appystore.mrecharge.app-main-57:/drawable/rounded_corner.xml"}, {"merged": "com.appystore.mrecharge.app-debug-55:/drawable_modern_toggle_selector.xml.flat", "source": "com.appystore.mrecharge.app-main-57:/drawable/modern_toggle_selector.xml"}, {"merged": "com.appystore.mrecharge.app-debug-55:/xml_network_security_config.xml.flat", "source": "com.appystore.mrecharge.app-main-57:/xml/network_security_config.xml"}, {"merged": "com.appystore.mrecharge.app-debug-55:/layout_settingsd.xml.flat", "source": "com.appystore.mrecharge.app-main-57:/layout/settingsd.xml"}, {"merged": "com.appystore.mrecharge.app-debug-55:/mipmap-hdpi_ic_launcher.png.flat", "source": "com.appystore.mrecharge.app-main-57:/mipmap-hdpi/ic_launcher.png"}, {"merged": "com.appystore.mrecharge.app-debug-55:/drawable_bg.png.flat", "source": "com.appystore.mrecharge.app-main-57:/drawable/bg.png"}, {"merged": "com.appystore.mrecharge.app-debug-55:/drawable_rounded_cornerss.xml.flat", "source": "com.appystore.mrecharge.app-main-57:/drawable/rounded_cornerss.xml"}, {"merged": "com.appystore.mrecharge.app-debug-55:/drawable_modern_button_background.xml.flat", "source": "com.appystore.mrecharge.app-main-57:/drawable/modern_button_background.xml"}, {"merged": "com.appystore.mrecharge.app-debug-55:/drawable_gradient_background.xml.flat", "source": "com.appystore.mrecharge.app-main-57:/drawable/gradient_background.xml"}, {"merged": "com.appystore.mrecharge.app-debug-55:/drawable_modern_toggle_on.xml.flat", "source": "com.appystore.mrecharge.app-main-57:/drawable/modern_toggle_on.xml"}, {"merged": "com.appystore.mrecharge.app-debug-55:/raw_notification.mp3.flat", "source": "com.appystore.mrecharge.app-main-57:/raw/notification.mp3"}, {"merged": "com.appystore.mrecharge.app-debug-55:/layout_license_activation.xml.flat", "source": "com.appystore.mrecharge.app-main-57:/layout/license_activation.xml"}, {"merged": "com.appystore.mrecharge.app-debug-55:/drawable_modern_status_background.xml.flat", "source": "com.appystore.mrecharge.app-main-57:/drawable/modern_status_background.xml"}, {"merged": "com.appystore.mrecharge.app-debug-55:/drawable_toggle_on.png.flat", "source": "com.appystore.mrecharge.app-main-57:/drawable/toggle_on.png"}, {"merged": "com.appystore.mrecharge.app-debug-55:/drawable_ic_settings_cell_black_24dp.xml.flat", "source": "com.appystore.mrecharge.app-main-57:/drawable/ic_settings_cell_black_24dp.xml"}, {"merged": "com.appystore.mrecharge.app-debug-55:/drawable_bgg.xml.flat", "source": "com.appystore.mrecharge.app-main-57:/drawable/bgg.xml"}, {"merged": "com.appystore.mrecharge.app-debug-55:/drawable_ic_launcher_background.xml.flat", "source": "com.appystore.mrecharge.app-main-57:/drawable/ic_launcher_background.xml"}, {"merged": "com.appystore.mrecharge.app-debug-55:/drawable_select.xml.flat", "source": "com.appystore.mrecharge.app-main-57:/drawable/select.xml"}, {"merged": "com.appystore.mrecharge.app-debug-55:/drawable_ic_stat_name.png.flat", "source": "com.appystore.mrecharge.app-main-57:/drawable/ic_stat_name.png"}, {"merged": "com.appystore.mrecharge.app-debug-55:/mipmap-mdpi_ic_launcher.png.flat", "source": "com.appystore.mrecharge.app-main-57:/mipmap-mdpi/ic_launcher.png"}, {"merged": "com.appystore.mrecharge.app-debug-55:/drawable_button_background.xml.flat", "source": "com.appystore.mrecharge.app-main-57:/drawable/button_background.xml"}, {"merged": "com.appystore.mrecharge.app-debug-55:/drawable_ic_baseline_add_to_queue_24.xml.flat", "source": "com.appystore.mrecharge.app-main-57:/drawable/ic_baseline_add_to_queue_24.xml"}, {"merged": "com.appystore.mrecharge.app-debug-55:/drawable_ic_settings_black_24dp.xml.flat", "source": "com.appystore.mrecharge.app-main-57:/drawable/ic_settings_black_24dp.xml"}, {"merged": "com.appystore.mrecharge.app-debug-55:/layout_config.xml.flat", "source": "com.appystore.mrecharge.app-main-57:/layout/config.xml"}, {"merged": "com.appystore.mrecharge.app-debug-55:/drawable_modern_edit_text_background.xml.flat", "source": "com.appystore.mrecharge.app-main-57:/drawable/modern_edit_text_background.xml"}, {"merged": "com.appystore.mrecharge.app-debug-55:/mipmap-xhdpi_ic_launcher.png.flat", "source": "com.appystore.mrecharge.app-main-57:/mipmap-xhdpi/ic_launcher.png"}, {"merged": "com.appystore.mrecharge.app-debug-55:/drawable_input_background.xml.flat", "source": "com.appystore.mrecharge.app-main-57:/drawable/input_background.xml"}, {"merged": "com.appystore.mrecharge.app-debug-55:/drawable_ic_launcher_foreground.xml.flat", "source": "com.appystore.mrecharge.app-main-57:/drawable/ic_launcher_foreground.xml"}, {"merged": "com.appystore.mrecharge.app-debug-55:/layout_forward.xml.flat", "source": "com.appystore.mrecharge.app-main-57:/layout/forward.xml"}, {"merged": "com.appystore.mrecharge.app-debug-55:/layout_activity_main.xml.flat", "source": "com.appystore.mrecharge.app-main-57:/layout/activity_main.xml"}, {"merged": "com.appystore.mrecharge.app-debug-55:/mipmap-xxxhdpi_ic_launcher.png.flat", "source": "com.appystore.mrecharge.app-main-57:/mipmap-xxxhdpi/ic_launcher.png"}, {"merged": "com.appystore.mrecharge.app-debug-55:/drawable_appystoremrecharge.png.flat", "source": "com.appystore.mrecharge.app-main-57:/drawable/appystoremrecharge.png"}, {"merged": "com.appystore.mrecharge.app-debug-55:/xml_ussd_service.xml.flat", "source": "com.appystore.mrecharge.app-main-57:/xml/ussd_service.xml"}, {"merged": "com.appystore.mrecharge.app-debug-55:/xml_data_extraction_rules.xml.flat", "source": "com.appystore.mrecharge.app-main-57:/xml/data_extraction_rules.xml"}, {"merged": "com.appystore.mrecharge.app-debug-55:/layout_item_todo.xml.flat", "source": "com.appystore.mrecharge.app-main-57:/layout/item_todo.xml"}]