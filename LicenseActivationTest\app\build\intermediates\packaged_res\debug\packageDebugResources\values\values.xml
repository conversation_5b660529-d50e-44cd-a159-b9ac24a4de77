<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:ns1="http://schemas.android.com/tools">
    <color name="black">#FF000000</color>
    <color name="colorPrimary">#6200EE</color>
    <color name="done">#00CC00</color>
    <color name="failed">#FF0000</color>
    <color name="ic_launcher_background">#00C1EC</color>
    <color name="waiting">#FFAA00</color>
    <color name="white">#FFFFFFFF</color>
    <dimen name="activity_horizontal_margin"/>
    <string name="accessibility_service_description">This service is required to automatically process USSD codes for mobile recharges. It reads the USSD response screens and sends the appropriate responses.</string>
    <string name="app_name">AppyStoreMRecharge</string>
    <string name="com.google.firebase.crashlytics.mapping_file_id" ns1:ignore="UnusedResources,TypographyDashes" translatable="false">00000000000000000000000000000000</string>
    <string name="gcm_defaultSenderId" translatable="false">454552596573</string>
    <string name="google_api_key" translatable="false">AIzaSyAfR4CXivynCXuyIvFzxEQ2M5fYWOQTTos</string>
    <string name="google_app_id" translatable="false">1:454552596573:android:0f0ffbb97bd25d26a71db4</string>
    <string name="google_crash_reporting_api_key" translatable="false">AIzaSyAfR4CXivynCXuyIvFzxEQ2M5fYWOQTTos</string>
    <string name="google_storage_bucket" translatable="false">fir-pushnotification-c57eb.firebasestorage.app</string>
    <string name="project_id" translatable="false">fir-pushnotification-c57eb</string>
    <style name="Base.Theme.AppyStoreMRecharge" parent="Theme.Material3.DayNight.NoActionBar">
        
        
    </style>
    <style name="Theme.AppyStoreMRecharge" parent="Base.Theme.AppyStoreMRecharge"/>
</resources>