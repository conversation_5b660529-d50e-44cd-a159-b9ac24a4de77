{"logs": [{"outputFile": "com.appystore.mrecharge.app-mergeDebugResources-53:/values-ru/values-ru.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\b4c8334a260b71f191b97485b06ad1d4\\transformed\\material-1.12.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,381,459,537,621,719,810,907,1044,1136,1211,1277,1376,1453,1516,1634,1695,1760,1817,1887,1948,2002,2118,2175,2237,2291,2365,2493,2581,2668,2771,2863,2949,3086,3170,3255,3389,3480,3556,3610,3661,3727,3799,3877,3948,4030,4110,4186,4263,4340,4447,4536,4609,4699,4794,4868,4949,5042,5097,5178,5244,5330,5415,5477,5541,5604,5676,5774,5873,5968,6060,6118,6173,6253,6347,6423", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "endColumns": "12,77,77,83,97,90,96,136,91,74,65,98,76,62,117,60,64,56,69,60,53,115,56,61,53,73,127,87,86,102,91,85,136,83,84,133,90,75,53,50,65,71,77,70,81,79,75,76,76,106,88,72,89,94,73,80,92,54,80,65,85,84,61,63,62,71,97,98,94,91,57,54,79,93,75,78", "endOffsets": "376,454,532,616,714,805,902,1039,1131,1206,1272,1371,1448,1511,1629,1690,1755,1812,1882,1943,1997,2113,2170,2232,2286,2360,2488,2576,2663,2766,2858,2944,3081,3165,3250,3384,3475,3551,3605,3656,3722,3794,3872,3943,4025,4105,4181,4258,4335,4442,4531,4604,4694,4789,4863,4944,5037,5092,5173,5239,5325,5410,5472,5536,5599,5671,5769,5868,5963,6055,6113,6168,6248,6342,6418,6497"}, "to": {"startLines": "2,37,38,39,40,41,49,50,51,71,72,73,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,139,140,141", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3377,3455,3533,3617,3715,4533,4630,4767,7240,7315,7381,7802,7879,7942,8060,8121,8186,8243,8313,8374,8428,8544,8601,8663,8717,8791,8919,9007,9094,9197,9289,9375,9512,9596,9681,9815,9906,9982,10036,10087,10153,10225,10303,10374,10456,10536,10612,10689,10766,10873,10962,11035,11125,11220,11294,11375,11468,11523,11604,11670,11756,11841,11903,11967,12030,12102,12200,12299,12394,12486,12544,12599,12761,12855,12931", "endLines": "7,37,38,39,40,41,49,50,51,71,72,73,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,139,140,141", "endColumns": "12,77,77,83,97,90,96,136,91,74,65,98,76,62,117,60,64,56,69,60,53,115,56,61,53,73,127,87,86,102,91,85,136,83,84,133,90,75,53,50,65,71,77,70,81,79,75,76,76,106,88,72,89,94,73,80,92,54,80,65,85,84,61,63,62,71,97,98,94,91,57,54,79,93,75,78", "endOffsets": "426,3450,3528,3612,3710,3801,4625,4762,4854,7310,7376,7475,7874,7937,8055,8116,8181,8238,8308,8369,8423,8539,8596,8658,8712,8786,8914,9002,9089,9192,9284,9370,9507,9591,9676,9810,9901,9977,10031,10082,10148,10220,10298,10369,10451,10531,10607,10684,10761,10868,10957,11030,11120,11215,11289,11370,11463,11518,11599,11665,11751,11836,11898,11962,12025,12097,12195,12294,12389,12481,12539,12594,12674,12850,12926,13005"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\b42671bce86d044f55bdf4e61e8361bd\\transformed\\appcompat-1.7.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,421,507,612,733,812,888,980,1074,1169,1262,1357,1451,1547,1642,1734,1826,1915,2021,2128,2226,2335,2442,2556,2722,2822", "endColumns": "114,101,98,85,104,120,78,75,91,93,94,92,94,93,95,94,91,91,88,105,106,97,108,106,113,165,99,81", "endOffsets": "215,317,416,502,607,728,807,883,975,1069,1164,1257,1352,1446,1542,1637,1729,1821,1910,2016,2123,2221,2330,2437,2551,2717,2817,2899"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "431,546,648,747,833,938,1059,1138,1214,1306,1400,1495,1588,1683,1777,1873,1968,2060,2152,2241,2347,2454,2552,2661,2768,2882,3048,12679", "endColumns": "114,101,98,85,104,120,78,75,91,93,94,92,94,93,95,94,91,91,88,105,106,97,108,106,113,165,99,81", "endOffsets": "541,643,742,828,933,1054,1133,1209,1301,1395,1490,1583,1678,1772,1868,1963,2055,2147,2236,2342,2449,2547,2656,2763,2877,3043,3143,12756"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\a8ddf21c5cfcfff775adf0ab8e498f49\\transformed\\credentials-1.2.0-rc01\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,165", "endColumns": "109,118", "endOffsets": "160,279"}, "to": {"startLines": "35,36", "startColumns": "4,4", "startOffsets": "3148,3258", "endColumns": "109,118", "endOffsets": "3253,3372"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\561943d2b2d87f776fb7a0e02865f59c\\transformed\\core-1.13.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,457,562,665,782", "endColumns": "97,101,100,100,104,102,116,100", "endOffsets": "148,250,351,452,557,660,777,878"}, "to": {"startLines": "42,43,44,45,46,47,48,142", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3806,3904,4006,4107,4208,4313,4416,13010", "endColumns": "97,101,100,100,104,102,116,100", "endOffsets": "3899,4001,4102,4203,4308,4411,4528,13106"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\812f317bcdb04d011f659e61565a4ab5\\transformed\\play-services-base-18.5.0\\res\\values-ru\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,458,580,686,824,949,1060,1160,1337,1440,1599,1721,1884,2038,2103,2159", "endColumns": "102,161,121,105,137,124,110,99,176,102,158,121,162,153,64,55,81", "endOffsets": "295,457,579,685,823,948,1059,1159,1336,1439,1598,1720,1883,2037,2102,2158,2240"}, "to": {"startLines": "52,53,54,55,56,57,58,59,61,62,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4859,4966,5132,5258,5368,5510,5639,5754,6015,6196,6303,6466,6592,6759,6917,6986,7046", "endColumns": "106,165,125,109,141,128,114,103,180,106,162,125,166,157,68,59,85", "endOffsets": "4961,5127,5253,5363,5505,5634,5749,5853,6191,6298,6461,6587,6754,6912,6981,7041,7127"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\f4e40cb66b2591bc7f67ef95d552f790\\transformed\\browser-1.4.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,163,268,380", "endColumns": "107,104,111,104", "endOffsets": "158,263,375,480"}, "to": {"startLines": "70,74,75,76", "startColumns": "4,4,4,4", "startOffsets": "7132,7480,7585,7697", "endColumns": "107,104,111,104", "endOffsets": "7235,7580,7692,7797"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\42a8a4ad246f20dee6506de075e12431\\transformed\\play-services-basement-18.5.0\\res\\values-ru\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "152", "endOffsets": "347"}, "to": {"startLines": "60", "startColumns": "4", "startOffsets": "5858", "endColumns": "156", "endOffsets": "6010"}}]}]}