http://schemas.android.com/apk/res-auto;;${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/colors.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/ic_launcher_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/gradient_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_stat_name.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/footer_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/toggle_on.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/bgg.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/select.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/bg.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/modern_edit_text_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/appystoremrecharge.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/save.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/nav_item_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/toggle_selector.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/modern_toggle_on.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/modern_button_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/rightorder.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/modern_status_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/button_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_settings_black_24dp.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/rounded_cornerss.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/card_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/modern_toggle_off.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/toggle_off.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_launcher_foreground.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_launcher_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/input_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/rounded_corner.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_baseline_add_to_queue_24.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_home_black_24dp.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_settings_cell_black_24dp.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/modern_toggle_selector.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/config.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/forward.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/settingsd.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/license_activation.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/bottom_navigation_menu.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/listmain.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_todo.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/menu_main.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/custom_dialog.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-mdpi/ic_launcher.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xhdpi/ic_launcher.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xxhdpi/ic_launcher.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xxxhdpi/ic_launcher.png,${\:app*release*MAIN*sourceProvider*0*resDir*0}/raw/notification.mp3,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/strings.xml,${\:app*buildDir}/generated/res/processReleaseGoogleServices/values/values.xml,${\:app*buildDir}/generated/res/injectCrashlyticsMappingFileIdRelease/values/com_google_firebase_crashlytics_mappingfileid.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/themes.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values-night/themes.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/xml/network_security_config.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/xml/data_extraction_rules.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/xml/ussd_service.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/xml/backup_rules.xml,+color:colorPrimary,0,V4000600dc,2e00060106,;"#6200EE";white,0,V400030061,**********,;"#FFFFFFFF";waiting,0,V40004008b,29000400b0,;"#FFAA00";ic_launcher_background,1,V400020039,380002006d,;"#00C1EC";black,0,V400020037,290002005c,;"#FF000000";failed,0,V40007010b,280007012f,;"#FF0000";done,0,V4000500b5,26000500d7,;"#00CC00";+dimen:activity_horizontal_margin,2,V400020039,2f00020064,;"";+drawable:gradient_background,3,F;ic_stat_name,4,F;footer_background,5,F;toggle_on,6,F;bgg,7,F;select,8,F;bg,9,F;modern_edit_text_background,10,F;appystoremrecharge,11,F;save,12,F;nav_item_background,13,F;toggle_selector,14,F;modern_toggle_on,15,F;modern_button_background,16,F;rightorder,17,F;modern_status_background,18,F;button_background,19,F;ic_settings_black_24dp,20,F;rounded_cornerss,21,F;card_background,22,F;modern_toggle_off,23,F;toggle_off,24,F;ic_launcher_foreground,25,F;ic_launcher_background,26,F;input_background,27,F;rounded_corner,28,F;ic_baseline_add_to_queue_24,29,F;ic_home_black_24dp,30,F;ic_settings_cell_black_24dp,31,F;modern_toggle_selector,32,F;+id:olmd,33,F;tt,34,F;auto,33,F;rocket,35,F;rocket,36,F;imageView,33,F;imageView,36,F;main,37,F;mon,33,F;mon,38,F;bank,33,F;bank,34,F;bank,35,F;bank,36,F;bank,38,F;expirationProgressBar,37,F;simsms,33,F;bkash,35,F;bkash,36,F;recycleView,39,F;bettery,33,F;dpin,33,F;itemLayout,40,F;upay,36,F;countdownText,37,F;inter,41,F;bill,36,F;gpt,34,F;nogad,35,F;nogad,36,F;license_activation,33,F;tvPrice,40,F;trys,35,F;sima_rocketj,34,F;at,34,F;domainLoginSection,37,F;sima_rocketm,34,F;status,40,F;next,33,F;save,34,F;bl,34,F;bks,35,F;btnSpeakContainer,33,F;btnSpeakContainer,34,F;btnSpeakContainer,35,F;btnSpeakContainer,36,F;licensekey,33,F;dialog_button,42,F;activity_main,33,F;activity_main,34,F;activity_main,35,F;activity_main,36,F;simaname,33,F;expirationText,37,F;pinField,37,F;sms,33,F;sms,34,F;sms,35,F;sms,36,F;sms,38,F;simbname,33,F;blb,35,F;blb,35,F;savedata,35,F;rks,35,F;ngs,35,F;licenseKeyField,37,F;simbc,33,F;sms_service,33,F;domainInfoText,37,F;forward,41,F;sav,33,F;gp,34,F;blt,34,F;activateButton,37,F;home,33,F;home,34,F;home,35,F;home,36,F;home,38,F;domainLoginButton,37,F;expirationSection,37,F;robi,34,F;myid,33,F;ttt,34,F;statusText,37,F;time,35,F;newm,33,F;server_type,33,F;dc,35,F;+layout:item_todo,40,F;activity_main,33,F;custom_dialog,42,F;settingsd,36,F;forward,35,F;listmain,39,F;license_activation,37,F;config,34,F;+menu:bottom_navigation_menu,38,F;menu_main,41,F;+mipmap:ic_launcher,43,F;ic_launcher,44,F;ic_launcher,45,F;ic_launcher,46,F;ic_launcher,47,F;+raw:notification,48,F;+string:app_name,49,V400010010,**********,;"AppyStoreMRecharge";google_app_id,50,V4000400f1,6c00040159,;"1\:454552596573\:android\:0f0ffbb97bd25d26a71db4";com.google.firebase.crashlytics.mapping_file_id,51,V8011e,ad000801cb,UnusedResources,TypographyDashes;"00000000000000000000000000000000";gcm_defaultSenderId,50,V400020037,**********,;"454552596573";google_api_key,50,V400030089,67000300ec,;"AIzaSyAfR4CXivynCXuyIvFzxEQ2M5fYWOQTTos";project_id,50,V40007024c,560007029e,;"fir-pushnotification-c57eb";google_crash_reporting_api_key,50,V40005015e,77000501d1,;"AIzaSyAfR4CXivynCXuyIvFzxEQ2M5fYWOQTTos";accessibility_service_description,49,V400020048,d80002011c,;"This service is required to automatically process USSD codes for mobile recharges. It reads the USSD response screens and sends the appropriate responses.";google_storage_bucket,50,V4000601d6,7500060247,;"fir-pushnotification-c57eb.firebasestorage.app";+style:Base.Theme.AppyStoreMRecharge,52,V400020064,c00050147,;DTheme.Material3.DayNight.NoActionBar,;Base.Theme.AppyStoreMRecharge,53,V400020064,c00050145,;DTheme.Material3.DayNight.NoActionBar,;Theme.AppyStoreMRecharge,52,V40007014d,540007019d,;DBase.Theme.AppyStoreMRecharge,;+xml:network_security_config,54,F;data_extraction_rules,55,F;ussd_service,56,F;backup_rules,57,F;