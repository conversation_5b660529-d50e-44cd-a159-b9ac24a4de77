1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.appystore.mrecharge"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-feature
11-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:5:5-7:36
12        android:name="android.hardware.telephony"
12-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:6:9-50
13        android:required="false" />
13-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:7:9-33
14
15    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
15-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:9:5-80
15-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:9:22-78
16    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
16-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:10:5-78
16-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:10:22-76
17    <uses-permission android:name="android.permission.INTERNET" />
17-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:11:5-66
17-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:11:22-64
18    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
18-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:12:5-75
18-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:12:22-73
19    <uses-permission android:name="android.permission.READ_SMS" />
19-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:13:5-66
19-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:13:22-64
20    <uses-permission android:name="android.permission.SEND_SMS" />
20-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:14:5-66
20-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:14:22-64
21    <uses-permission android:name="android.permission.RECEIVE_SMS" />
21-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:15:5-69
21-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:15:22-67
22    <uses-permission android:name="android.permission.CALL_PHONE" />
22-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:16:5-68
22-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:16:22-66
23    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
23-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:17:5-74
23-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:17:22-72
24    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
24-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:18:5-76
24-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:18:22-74
25    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_PHONE_CALL" />
25-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:19:5-87
25-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:19:22-85
26    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
26-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:20:5-86
26-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:20:22-84
27    <uses-permission android:name="android.permission.WAKE_LOCK" />
27-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:21:5-67
27-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:21:22-65
28    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
28-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:22:5-74
28-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:22:22-72
29    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
29-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:23:5-94
29-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:23:22-92
30    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
30-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:24:5-77
30-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:24:22-75
31    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
31-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:25:5-81
31-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:25:22-79
32    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
32-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:26:5-109
32-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:26:22-107
33
34    <!-- Android 15 specific permissions -->
35    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
35-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:29:5-76
35-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:29:22-74
36    <uses-permission android:name="android.permission.USE_EXACT_ALARM" />
36-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:30:5-73
36-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:30:22-71
37    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
37-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:31:5-78
37-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:31:22-76
38    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
38-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\98a93d668684a960bab7b3805beee81d\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:25:5-79
38-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\98a93d668684a960bab7b3805beee81d\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:25:22-76
39    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
39-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\98a93d668684a960bab7b3805beee81d\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:26:5-88
39-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\98a93d668684a960bab7b3805beee81d\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:26:22-85
40    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
40-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\98a93d668684a960bab7b3805beee81d\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:27:5-82
40-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\98a93d668684a960bab7b3805beee81d\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:27:22-79
41    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
41-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b3f8d4f2fa635740972b39dbfb64c7bc\transformed\recaptcha-18.6.1\AndroidManifest.xml:9:5-98
41-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b3f8d4f2fa635740972b39dbfb64c7bc\transformed\recaptcha-18.6.1\AndroidManifest.xml:9:22-95
42
43    <permission
43-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\561943d2b2d87f776fb7a0e02865f59c\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
44        android:name="com.appystore.mrecharge.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
44-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\561943d2b2d87f776fb7a0e02865f59c\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
45        android:protectionLevel="signature" />
45-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\561943d2b2d87f776fb7a0e02865f59c\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
46
47    <uses-permission android:name="com.appystore.mrecharge.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
47-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\561943d2b2d87f776fb7a0e02865f59c\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
47-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\561943d2b2d87f776fb7a0e02865f59c\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
48
49    <application
49-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:33:5-316:19
50        android:allowBackup="true"
50-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:34:9-35
51        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
51-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\561943d2b2d87f776fb7a0e02865f59c\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
52        android:dataExtractionRules="@xml/data_extraction_rules"
52-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:35:9-65
53        android:debuggable="true"
54        android:extractNativeLibs="false"
55        android:fullBackupContent="@xml/backup_rules"
55-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:36:9-54
56        android:icon="@mipmap/ic_launcher"
56-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:37:9-43
57        android:label="@string/app_name"
57-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:38:9-41
58        android:networkSecurityConfig="@xml/network_security_config"
58-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:42:9-69
59        android:roundIcon="@mipmap/ic_launcher"
59-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:39:9-48
60        android:supportsRtl="true"
60-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:40:9-35
61        android:testOnly="true"
62        android:theme="@style/Theme.AppyStoreMRecharge"
62-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:41:9-56
63        android:usesCleartextTraffic="true" >
63-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:43:9-44
64        <uses-library
64-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:45:9-47:39
65            android:name="org.apache.http.legacy"
65-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:46:13-50
66            android:required="false" />
66-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:47:13-37
67
68        <activity
68-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:48:9-56:20
69            android:name="com.appystore.mrecharge.activity.MainActivity"
69-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:51:13-73
70            android:exported="true"
70-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:49:13-36
71            android:label="@string/app_name" >
71-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:50:13-45
72            <intent-filter>
72-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:52:13-55:29
73                <action android:name="android.intent.action.MAIN" />
73-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:53:17-68
73-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:53:25-66
74
75                <category android:name="android.intent.category.LAUNCHER" />
75-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:54:17-76
75-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:54:27-74
76            </intent-filter>
77        </activity>
78
79        <receiver
79-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:57:9-62:20
80            android:name="com.appystore.mrecharge.IncomingSms"
80-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:57:19-69
81            android:exported="true" >
81-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:58:13-36
82            <intent-filter android:priority="1000" >
82-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:59:13-61:29
82-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:59:28-51
83                <action android:name="android.provider.Telephony.SMS_RECEIVED" />
83-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:60:17-81
83-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:60:25-79
84            </intent-filter>
85        </receiver>
86
87        <service
87-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:63:9-68:19
88            android:name="com.appystore.mrecharge.service.PushService"
88-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:63:18-76
89            android:exported="false" >
89-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:64:13-37
90            <intent-filter>
90-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:65:13-67:29
91                <action android:name="com.google.firebase.MESSAGING_EVENT" />
91-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:66:17-77
91-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:66:25-75
92            </intent-filter>
93        </service>
94        <service android:name="com.appystore.mrecharge.service.Recharge" />
94-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:69:9-75
94-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:69:18-73
95        <service
95-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:70:9-75:65
96            android:name="com.appystore.mrecharge.service.sever"
96-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:71:13-65
97            android:enabled="true"
97-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:72:13-35
98            android:exported="true"
98-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:73:13-36
99            android:foregroundServiceType="phoneCall|dataSync"
99-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:75:13-63
100            android:stopWithTask="false" />
100-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:74:13-41
101        <service android:name="com.appystore.mrecharge.service.Smsend" />
101-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:76:9-73
101-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:76:18-71
102        <service android:name="com.appystore.mrecharge.service.International_service" />
102-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:77:9-88
102-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:77:18-86
103        <service
103-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:78:9-88:19
104            android:name="com.appystore.mrecharge.service.USSDService"
104-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:79:13-71
105            android:exported="true"
105-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:81:13-36
106            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
106-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:80:13-79
107            <intent-filter>
107-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:82:13-84:29
108                <action android:name="android.accessibilityservice.AccessibilityService" />
108-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:83:17-91
108-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:83:25-89
109            </intent-filter>
110
111            <meta-data
111-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:85:13-87:55
112                android:name="android.accessibilityservice"
112-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:86:17-60
113                android:resource="@xml/ussd_service" />
113-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:87:17-53
114        </service>
115
116        <activity
116-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:89:9-102
117            android:name="com.appystore.mrecharge.activity.Settings"
117-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:89:19-75
118            android:exported="false" />
118-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:89:76-100
119        <activity
119-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:90:9-104
120            android:name="com.appystore.mrecharge.activity.intsetting"
120-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:90:19-77
121            android:exported="false" />
121-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:90:78-102
122        <activity
122-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:91:9-104
123            android:name="com.appystore.mrecharge.activity.Monitoring"
123-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:91:19-77
124            android:exported="false" />
124-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:91:78-102
125
126        <provider
126-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:92:9-99:20
127            android:name="androidx.startup.InitializationProvider"
127-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:93:13-67
128            android:authorities="com.appystore.mrecharge.androidx-startup"
128-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:95:13-75
129            android:exported="false" >
129-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:94:13-37
130            <meta-data
130-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:96:13-98:51
131                android:name="androidx.work.WorkManagerInitializer"
131-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:97:17-68
132                android:value="androidx.startup" />
132-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:98:17-49
133            <meta-data
133-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\07fb0fc7f017af5a92250a9f8c8cb2fa\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
134                android:name="androidx.emoji2.text.EmojiCompatInitializer"
134-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\07fb0fc7f017af5a92250a9f8c8cb2fa\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
135                android:value="androidx.startup" />
135-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\07fb0fc7f017af5a92250a9f8c8cb2fa\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
136            <meta-data
136-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\c46d78812b95fd28ddbfb400e76e1261\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
137                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
137-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\c46d78812b95fd28ddbfb400e76e1261\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
138                android:value="androidx.startup" />
138-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\c46d78812b95fd28ddbfb400e76e1261\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
139            <meta-data
139-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c7263e1c10130702e5eff4afa7938ce6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
140                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
140-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c7263e1c10130702e5eff4afa7938ce6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
141                android:value="androidx.startup" />
141-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c7263e1c10130702e5eff4afa7938ce6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
142        </provider>
143
144        <service
144-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:100:9-104:46
145            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
145-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:101:13-88
146            android:directBootAware="false"
146-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:104:13-44
147            android:enabled="@bool/enable_system_alarm_service_default"
147-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:102:13-72
148            android:exported="false" />
148-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:103:13-37
149        <service
149-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:105:9-110:46
150            android:name="androidx.work.impl.background.systemjob.SystemJobService"
150-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:106:13-84
151            android:directBootAware="false"
151-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:110:13-44
152            android:enabled="@bool/enable_system_job_service_default"
152-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:108:13-70
153            android:exported="true"
153-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:109:13-36
154            android:permission="android.permission.BIND_JOB_SERVICE" />
154-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:107:13-69
155        <service
155-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:111:9-115:46
156            android:name="androidx.work.impl.foreground.SystemForegroundService"
156-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:112:13-81
157            android:directBootAware="false"
157-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:115:13-44
158            android:enabled="@bool/enable_system_foreground_service_default"
158-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:113:13-77
159            android:exported="false" />
159-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:114:13-37
160
161        <receiver
161-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:116:9-120:46
162            android:name="androidx.work.impl.utils.ForceStopRunnable.BroadcastReceiver"
162-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:117:13-88
163            android:directBootAware="false"
163-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:120:13-44
164            android:enabled="true"
164-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:118:13-35
165            android:exported="false" />
165-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:119:13-37
166        <receiver
166-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:121:9-130:20
167            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy.BatteryChargingProxy"
167-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:122:13-106
168            android:directBootAware="false"
168-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:125:13-44
169            android:enabled="false"
169-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:123:13-36
170            android:exported="false" >
170-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:124:13-37
171            <intent-filter>
171-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:126:13-129:29
172                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
172-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:127:17-86
172-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:127:25-84
173                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
173-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:128:17-89
173-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:128:25-87
174            </intent-filter>
175        </receiver>
176        <receiver
176-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:131:9-140:20
177            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy.BatteryNotLowProxy"
177-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:132:13-104
178            android:directBootAware="false"
178-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:135:13-44
179            android:enabled="false"
179-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:133:13-36
180            android:exported="false" >
180-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:134:13-37
181            <intent-filter>
181-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:136:13-139:29
182                <action android:name="android.intent.action.BATTERY_OKAY" />
182-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:137:17-76
182-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:137:25-74
183                <action android:name="android.intent.action.BATTERY_LOW" />
183-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:138:17-75
183-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:138:25-73
184            </intent-filter>
185        </receiver>
186        <receiver
186-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:141:9-150:20
187            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy.StorageNotLowProxy"
187-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:142:13-104
188            android:directBootAware="false"
188-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:145:13-44
189            android:enabled="false"
189-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:143:13-36
190            android:exported="false" >
190-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:144:13-37
191            <intent-filter>
191-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:146:13-149:29
192                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
192-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:147:17-82
192-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:147:25-80
193                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
193-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:148:17-81
193-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:148:25-79
194            </intent-filter>
195        </receiver>
196        <receiver
196-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:151:9-159:20
197            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy.NetworkStateProxy"
197-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:152:13-103
198            android:directBootAware="false"
198-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:155:13-44
199            android:enabled="false"
199-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:153:13-36
200            android:exported="false" >
200-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:154:13-37
201            <intent-filter>
201-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:156:13-158:29
202                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
202-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:157:17-78
202-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:157:25-76
203            </intent-filter>
204        </receiver>
205        <receiver
205-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:160:9-170:20
206            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
206-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:161:13-88
207            android:directBootAware="false"
207-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:164:13-44
208            android:enabled="false"
208-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:162:13-36
209            android:exported="false" >
209-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:163:13-37
210            <intent-filter>
210-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:165:13-169:29
211                <action android:name="android.intent.action.BOOT_COMPLETED" />
211-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:166:17-78
211-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:166:25-76
212                <action android:name="android.intent.action.TIME_SET" />
212-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:167:17-72
212-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:167:25-70
213                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
213-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:168:17-80
213-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:168:25-78
214            </intent-filter>
215        </receiver>
216        <receiver
216-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:171:9-179:20
217            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
217-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:172:13-99
218            android:directBootAware="false"
218-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:175:13-44
219            android:enabled="@bool/enable_system_alarm_service_default"
219-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:173:13-72
220            android:exported="false" >
220-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:174:13-37
221            <intent-filter>
221-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:176:13-178:29
222                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
222-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:177:17-97
222-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:177:25-95
223            </intent-filter>
224        </receiver>
225        <receiver
225-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:180:9-189:20
226            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
226-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:181:13-78
227            android:directBootAware="false"
227-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:185:13-44
228            android:enabled="true"
228-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:183:13-35
229            android:exported="true"
229-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:184:13-36
230            android:permission="android.permission.DUMP" >
230-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:182:13-57
231            <intent-filter>
231-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:186:13-188:29
232                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
232-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:187:17-87
232-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:187:25-85
233            </intent-filter>
234        </receiver>
235
236        <activity
236-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:190:9-205:20
237            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
237-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:192:13-80
238            android:excludeFromRecents="true"
238-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:194:13-46
239            android:exported="true"
239-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:193:13-36
240            android:launchMode="singleTask"
240-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:195:13-44
241            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
241-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:191:13-72
242            <intent-filter>
242-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:196:13-204:29
243                <action android:name="android.intent.action.VIEW" />
243-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:197:17-68
243-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:197:25-66
244
245                <category android:name="android.intent.category.DEFAULT" />
245-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:198:17-75
245-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:198:27-73
246                <category android:name="android.intent.category.BROWSABLE" />
246-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:199:17-77
246-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:199:27-75
247
248                <data
248-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:200:17-203:39
249                    android:host="firebase.auth"
249-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:202:21-49
250                    android:path="/"
250-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:203:21-37
251                    android:scheme="genericidp" />
251-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:201:21-48
252            </intent-filter>
253        </activity>
254        <activity
254-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:206:9-221:20
255            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
255-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:208:13-79
256            android:excludeFromRecents="true"
256-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:210:13-46
257            android:exported="true"
257-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:209:13-36
258            android:launchMode="singleTask"
258-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:211:13-44
259            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
259-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:207:13-72
260            <intent-filter>
260-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:212:13-220:29
261                <action android:name="android.intent.action.VIEW" />
261-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:197:17-68
261-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:197:25-66
262
263                <category android:name="android.intent.category.DEFAULT" />
263-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:198:17-75
263-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:198:27-73
264                <category android:name="android.intent.category.BROWSABLE" />
264-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:199:17-77
264-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:199:27-75
265
266                <data
266-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:200:17-203:39
267                    android:host="firebase.auth"
267-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:202:21-49
268                    android:path="/"
268-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:203:21-37
269                    android:scheme="recaptcha" />
269-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:201:21-48
270            </intent-filter>
271        </activity>
272
273        <service
273-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:222:9-230:19
274            android:name="com.google.firebase.auth.api.fallback.service.FirebaseAuthFallbackService"
274-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:223:13-101
275            android:enabled="true"
275-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:224:13-35
276            android:exported="false" >
276-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:225:13-37
277            <intent-filter>
277-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:226:13-229:29
278                <action android:name="com.google.firebase.auth.api.gms.service.START" />
278-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:227:17-88
278-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:227:25-86
279
280                <category android:name="android.intent.category.DEFAULT" />
280-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:198:17-75
280-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:198:27-73
281            </intent-filter>
282        </service>
283        <service
283-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:231:9-253:19
284            android:name="com.google.firebase.components.ComponentDiscoveryService"
284-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:232:13-84
285            android:directBootAware="true"
285-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:234:13-43
286            android:exported="false" >
286-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:233:13-37
287            <meta-data
287-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:235:13-237:84
288                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
288-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:236:17-109
289                android:value="com.google.firebase.components.ComponentRegistrar" />
289-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:237:17-82
290            <meta-data
290-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:238:13-240:84
291                android:name="com.google.firebase.components:com.google.firebase.crashlytics.CrashlyticsRegistrar"
291-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:239:17-115
292                android:value="com.google.firebase.components.ComponentRegistrar" />
292-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:240:17-82
293            <meta-data
293-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:241:13-243:84
294                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
294-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:242:17-119
295                android:value="com.google.firebase.components.ComponentRegistrar" />
295-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:243:17-82
296            <meta-data
296-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:244:13-246:84
297                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
297-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:245:17-115
298                android:value="com.google.firebase.components.ComponentRegistrar" />
298-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:246:17-82
299            <meta-data
299-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:247:13-249:84
300                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
300-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:248:17-139
301                android:value="com.google.firebase.components.ComponentRegistrar" />
301-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:249:17-82
302            <meta-data
302-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:250:13-252:84
303                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
303-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:251:17-127
304                android:value="com.google.firebase.components.ComponentRegistrar" />
304-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:252:17-82
305            <meta-data
305-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\b6f59ff4ed6d94db3d1663a58acae718\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:57:13-59:85
306                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
306-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\b6f59ff4ed6d94db3d1663a58acae718\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:58:17-122
307                android:value="com.google.firebase.components.ComponentRegistrar" />
307-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\b6f59ff4ed6d94db3d1663a58acae718\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:59:17-82
308            <meta-data
308-->[com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.14\transforms\8c03f49c2d7a4b0abf4600a2b71db677\transformed\firebase-crashlytics-19.4.4\AndroidManifest.xml:15:13-17:85
309                android:name="com.google.firebase.components:com.google.firebase.crashlytics.FirebaseCrashlyticsKtxRegistrar"
309-->[com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.14\transforms\8c03f49c2d7a4b0abf4600a2b71db677\transformed\firebase-crashlytics-19.4.4\AndroidManifest.xml:16:17-126
310                android:value="com.google.firebase.components.ComponentRegistrar" />
310-->[com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.14\transforms\8c03f49c2d7a4b0abf4600a2b71db677\transformed\firebase-crashlytics-19.4.4\AndroidManifest.xml:17:17-82
311            <meta-data
311-->[com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\493565d3d4690f5a6802e799ea942eae\transformed\firebase-sessions-2.1.2\AndroidManifest.xml:29:13-31:85
312                android:name="com.google.firebase.components:com.google.firebase.sessions.FirebaseSessionsRegistrar"
312-->[com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\493565d3d4690f5a6802e799ea942eae\transformed\firebase-sessions-2.1.2\AndroidManifest.xml:30:17-117
313                android:value="com.google.firebase.components.ComponentRegistrar" />
313-->[com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\493565d3d4690f5a6802e799ea942eae\transformed\firebase-sessions-2.1.2\AndroidManifest.xml:31:17-82
314            <meta-data
314-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\e0122238bc1242e610e88fa5cc869d16\transformed\firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
315                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
315-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\e0122238bc1242e610e88fa5cc869d16\transformed\firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
316                android:value="com.google.firebase.components.ComponentRegistrar" />
316-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\e0122238bc1242e610e88fa5cc869d16\transformed\firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
317            <meta-data
317-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2a00c4536c133552601fa75968115f9a\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
318                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
318-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2a00c4536c133552601fa75968115f9a\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
319                android:value="com.google.firebase.components.ComponentRegistrar" />
319-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2a00c4536c133552601fa75968115f9a\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
320            <meta-data
320-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\ef6119fb565be338df0859ad382c537a\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
321                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
321-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\ef6119fb565be338df0859ad382c537a\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
322                android:value="com.google.firebase.components.ComponentRegistrar" />
322-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\ef6119fb565be338df0859ad382c537a\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
323        </service>
324
325        <receiver
325-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:254:9-261:20
326            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
326-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:255:13-78
327            android:exported="true"
327-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:257:13-36
328            android:permission="com.google.android.c2dm.permission.SEND" >
328-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:256:13-73
329            <intent-filter>
329-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:258:13-260:29
330                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
330-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:259:17-80
330-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:259:25-78
331            </intent-filter>
332
333            <meta-data
333-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\b6f59ff4ed6d94db3d1663a58acae718\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:37:13-39:40
334                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
334-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\b6f59ff4ed6d94db3d1663a58acae718\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:38:17-92
335                android:value="true" />
335-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\b6f59ff4ed6d94db3d1663a58acae718\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:39:17-37
336        </receiver>
337
338        <service
338-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:262:9-269:19
339            android:name="com.google.firebase.messaging.FirebaseMessagingService"
339-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:263:13-82
340            android:directBootAware="true"
340-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:265:13-43
341            android:exported="false" >
341-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:264:13-37
342            <intent-filter android:priority="-500" >
342-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:65:13-67:29
342-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:266:28-51
343                <action android:name="com.google.firebase.MESSAGING_EVENT" />
343-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:66:17-77
343-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:66:25-75
344            </intent-filter>
345        </service>
346        <service
346-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:270:9-276:19
347            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
347-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:271:13-103
348            android:exported="false" >
348-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:272:13-37
349            <meta-data
349-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:273:13-275:38
350                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
350-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:274:17-94
351                android:value="cct" />
351-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:275:17-36
352        </service>
353
354        <provider
354-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:277:9-282:45
355            android:name="com.google.firebase.provider.FirebaseInitProvider"
355-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:278:13-77
356            android:authorities="com.appystore.mrecharge.firebaseinitprovider"
356-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:280:13-79
357            android:directBootAware="true"
357-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:282:13-43
358            android:exported="false"
358-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:279:13-37
359            android:initOrder="100" />
359-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:281:13-36
360
361        <activity
361-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:283:9-286:39
362            android:name="com.google.android.gms.common.api.GoogleApiActivity"
362-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:285:13-79
363            android:exported="false"
363-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:286:13-37
364            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
364-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:284:13-72
365
366        <receiver
366-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:287:9-290:39
367            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
367-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:288:13-85
368            android:enabled="true"
368-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:289:13-35
369            android:exported="false" />
369-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:290:13-37
370
371        <service
371-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:291:9-294:39
372            android:name="com.google.android.gms.measurement.AppMeasurementService"
372-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:292:13-84
373            android:enabled="true"
373-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:293:13-35
374            android:exported="false" />
374-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:294:13-37
375        <service
375-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:295:9-299:39
376            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
376-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:296:13-87
377            android:enabled="true"
377-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:298:13-35
378            android:exported="false"
378-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:299:13-37
379            android:permission="android.permission.BIND_JOB_SERVICE" />
379-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:297:13-69
380
381        <meta-data
381-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:300:9-302:68
382            android:name="com.google.android.gms.version"
382-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:301:13-58
383            android:value="@integer/google_play_services_version" />
383-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:302:13-66
384
385        <service
385-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:303:9-306:45
386            android:name="androidx.room.MultiInstanceInvalidationService"
386-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:304:13-74
387            android:directBootAware="true"
387-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:306:13-43
388            android:exported="false" />
388-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:305:13-37
389        <service
389-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:307:9-310:39
390            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
390-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:308:13-117
391            android:exported="false"
391-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:310:13-37
392            android:permission="android.permission.BIND_JOB_SERVICE" />
392-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:309:13-69
393
394        <receiver
394-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:311:9-313:39
395            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
395-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:312:13-132
396            android:exported="false" />
396-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:313:13-37
397
398        <service
398-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\8dd12166401814958a84a1864c86ce1b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
399            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
399-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\8dd12166401814958a84a1864c86ce1b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
400            android:enabled="true"
400-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\8dd12166401814958a84a1864c86ce1b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
401            android:exported="false" >
401-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\8dd12166401814958a84a1864c86ce1b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
402            <meta-data
402-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\8dd12166401814958a84a1864c86ce1b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
403                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
403-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\8dd12166401814958a84a1864c86ce1b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
404                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
404-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\8dd12166401814958a84a1864c86ce1b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
405        </service>
406
407        <activity
407-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\8dd12166401814958a84a1864c86ce1b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
408            android:name="androidx.credentials.playservices.HiddenActivity"
408-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\8dd12166401814958a84a1864c86ce1b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
409            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
409-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\8dd12166401814958a84a1864c86ce1b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
410            android:enabled="true"
410-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\8dd12166401814958a84a1864c86ce1b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
411            android:exported="false"
411-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\8dd12166401814958a84a1864c86ce1b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
412            android:fitsSystemWindows="true"
412-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\8dd12166401814958a84a1864c86ce1b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
413            android:theme="@style/Theme.Hidden" >
413-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\8dd12166401814958a84a1864c86ce1b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
414        </activity>
415        <activity
415-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8b2512062bb9036d84302ff507f29fba\transformed\play-services-auth-20.7.0\AndroidManifest.xml:23:9-27:75
416            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
416-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8b2512062bb9036d84302ff507f29fba\transformed\play-services-auth-20.7.0\AndroidManifest.xml:24:13-93
417            android:excludeFromRecents="true"
417-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8b2512062bb9036d84302ff507f29fba\transformed\play-services-auth-20.7.0\AndroidManifest.xml:25:13-46
418            android:exported="false"
418-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8b2512062bb9036d84302ff507f29fba\transformed\play-services-auth-20.7.0\AndroidManifest.xml:26:13-37
419            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
419-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8b2512062bb9036d84302ff507f29fba\transformed\play-services-auth-20.7.0\AndroidManifest.xml:27:13-72
420        <!--
421            Service handling Google Sign-In user revocation. For apps that do not integrate with
422            Google Sign-In, this service will never be started.
423        -->
424        <service
424-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8b2512062bb9036d84302ff507f29fba\transformed\play-services-auth-20.7.0\AndroidManifest.xml:33:9-37:51
425            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
425-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8b2512062bb9036d84302ff507f29fba\transformed\play-services-auth-20.7.0\AndroidManifest.xml:34:13-89
426            android:exported="true"
426-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8b2512062bb9036d84302ff507f29fba\transformed\play-services-auth-20.7.0\AndroidManifest.xml:35:13-36
427            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
427-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8b2512062bb9036d84302ff507f29fba\transformed\play-services-auth-20.7.0\AndroidManifest.xml:36:13-107
428            android:visibleToInstantApps="true" />
428-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8b2512062bb9036d84302ff507f29fba\transformed\play-services-auth-20.7.0\AndroidManifest.xml:37:13-48
429        <service
429-->[com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\493565d3d4690f5a6802e799ea942eae\transformed\firebase-sessions-2.1.2\AndroidManifest.xml:22:9-25:40
430            android:name="com.google.firebase.sessions.SessionLifecycleService"
430-->[com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\493565d3d4690f5a6802e799ea942eae\transformed\firebase-sessions-2.1.2\AndroidManifest.xml:23:13-80
431            android:enabled="true"
431-->[com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\493565d3d4690f5a6802e799ea942eae\transformed\firebase-sessions-2.1.2\AndroidManifest.xml:24:13-35
432            android:exported="false" />
432-->[com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\493565d3d4690f5a6802e799ea942eae\transformed\firebase-sessions-2.1.2\AndroidManifest.xml:25:13-37
433
434        <receiver
434-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:61:9-66:35
435            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
435-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:62:13-88
436            android:directBootAware="false"
436-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:63:13-44
437            android:enabled="true"
437-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:64:13-35
438            android:exported="false" />
438-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:65:13-37
439        <receiver
439-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:67:9-77:20
440            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
440-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:68:13-106
441            android:directBootAware="false"
441-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:69:13-44
442            android:enabled="false"
442-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:70:13-36
443            android:exported="false" >
443-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:71:13-37
444            <intent-filter>
444-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:126:13-129:29
445                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
445-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:127:17-86
445-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:127:25-84
446                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
446-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:128:17-89
446-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:128:25-87
447            </intent-filter>
448        </receiver>
449        <receiver
449-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:78:9-88:20
450            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
450-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:79:13-104
451            android:directBootAware="false"
451-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:80:13-44
452            android:enabled="false"
452-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:81:13-36
453            android:exported="false" >
453-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:82:13-37
454            <intent-filter>
454-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:136:13-139:29
455                <action android:name="android.intent.action.BATTERY_OKAY" />
455-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:137:17-76
455-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:137:25-74
456                <action android:name="android.intent.action.BATTERY_LOW" />
456-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:138:17-75
456-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:138:25-73
457            </intent-filter>
458        </receiver>
459        <receiver
459-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:89:9-99:20
460            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
460-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:90:13-104
461            android:directBootAware="false"
461-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:91:13-44
462            android:enabled="false"
462-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:92:13-36
463            android:exported="false" >
463-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:93:13-37
464            <intent-filter>
464-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:146:13-149:29
465                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
465-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:147:17-82
465-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:147:25-80
466                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
466-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:148:17-81
466-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:148:25-79
467            </intent-filter>
468        </receiver>
469        <receiver
469-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:100:9-109:20
470            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
470-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:101:13-103
471            android:directBootAware="false"
471-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:102:13-44
472            android:enabled="false"
472-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:103:13-36
473            android:exported="false" >
473-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:104:13-37
474            <intent-filter>
474-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:156:13-158:29
475                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
475-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:157:17-78
475-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:157:25-76
476            </intent-filter>
477        </receiver>
478
479        <uses-library
479-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14\transforms\0f90a3bf636d390373b4501e1b796823\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:23:9-25:40
480            android:name="android.ext.adservices"
480-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14\transforms\0f90a3bf636d390373b4501e1b796823\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:24:13-50
481            android:required="false" />
481-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14\transforms\0f90a3bf636d390373b4501e1b796823\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:25:13-37
482
483        <receiver
483-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c7263e1c10130702e5eff4afa7938ce6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
484            android:name="androidx.profileinstaller.ProfileInstallReceiver"
484-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c7263e1c10130702e5eff4afa7938ce6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
485            android:directBootAware="false"
485-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c7263e1c10130702e5eff4afa7938ce6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
486            android:enabled="true"
486-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c7263e1c10130702e5eff4afa7938ce6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
487            android:exported="true"
487-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c7263e1c10130702e5eff4afa7938ce6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
488            android:permission="android.permission.DUMP" >
488-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c7263e1c10130702e5eff4afa7938ce6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
489            <intent-filter>
489-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c7263e1c10130702e5eff4afa7938ce6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
490                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
490-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c7263e1c10130702e5eff4afa7938ce6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
490-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c7263e1c10130702e5eff4afa7938ce6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
491            </intent-filter>
492            <intent-filter>
492-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c7263e1c10130702e5eff4afa7938ce6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
493                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
493-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c7263e1c10130702e5eff4afa7938ce6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
493-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c7263e1c10130702e5eff4afa7938ce6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
494            </intent-filter>
495            <intent-filter>
495-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c7263e1c10130702e5eff4afa7938ce6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
496                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
496-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c7263e1c10130702e5eff4afa7938ce6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
496-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c7263e1c10130702e5eff4afa7938ce6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
497            </intent-filter>
498            <intent-filter>
498-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c7263e1c10130702e5eff4afa7938ce6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
499                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
499-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c7263e1c10130702e5eff4afa7938ce6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
499-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c7263e1c10130702e5eff4afa7938ce6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
500            </intent-filter>
501        </receiver> <!-- The activities will be merged into the manifest of the hosting app. -->
502        <activity
502-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\3f6b128159a1ce82a882c0b825dba2e7\transformed\core-common-2.0.3\AndroidManifest.xml:14:9-18:65
503            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
503-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\3f6b128159a1ce82a882c0b825dba2e7\transformed\core-common-2.0.3\AndroidManifest.xml:15:13-93
504            android:exported="false"
504-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\3f6b128159a1ce82a882c0b825dba2e7\transformed\core-common-2.0.3\AndroidManifest.xml:16:13-37
505            android:stateNotNeeded="true"
505-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\3f6b128159a1ce82a882c0b825dba2e7\transformed\core-common-2.0.3\AndroidManifest.xml:17:13-42
506            android:theme="@style/Theme.PlayCore.Transparent" />
506-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\3f6b128159a1ce82a882c0b825dba2e7\transformed\core-common-2.0.3\AndroidManifest.xml:18:13-62
507    </application>
508
509</manifest>
