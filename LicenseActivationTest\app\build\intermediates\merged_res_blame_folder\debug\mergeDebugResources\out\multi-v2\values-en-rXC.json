{"logs": [{"outputFile": "com.appystore.mrecharge.app-mergeDebugResources-53:/values-en-rXC/values-en-rXC.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\f4e40cb66b2591bc7f67ef95d552f790\\transformed\\browser-1.4.0\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,255,454,665", "endColumns": "199,198,210,201", "endOffsets": "250,449,660,862"}, "to": {"startLines": "38,39,40,41", "startColumns": "4,4,4,4", "startOffsets": "7386,7586,7785,7996", "endColumns": "199,198,210,201", "endOffsets": "7581,7780,7991,8193"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\b42671bce86d044f55bdf4e61e8361bd\\transformed\\appcompat-1.7.0\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,312,515,725,912,1113,1329,1509,1684,1878,2072,2267,2464,2663,2858,3056,3253,3447,3641,3826,4031,4234,4435,4641,4846,5053,5327,5528", "endColumns": "206,202,209,186,200,215,179,174,193,193,194,196,198,194,197,196,193,193,184,204,202,200,205,204,206,273,200,185", "endOffsets": "307,510,720,907,1108,1324,1504,1679,1873,2067,2262,2459,2658,2853,3051,3248,3442,3636,3821,4026,4229,4430,4636,4841,5048,5322,5523,5709"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,42", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,312,515,725,912,1113,1329,1509,1684,1878,2072,2267,2464,2663,2858,3056,3253,3447,3641,3826,4031,4234,4435,4641,4846,5053,5327,8198", "endColumns": "206,202,209,186,200,215,179,174,193,193,194,196,198,194,197,196,193,193,184,204,202,200,205,204,206,273,200,185", "endOffsets": "307,510,720,907,1108,1324,1504,1679,1873,2067,2262,2459,2658,2853,3051,3248,3442,3636,3821,4026,4229,4430,4636,4841,5048,5322,5523,8379"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\a8ddf21c5cfcfff775adf0ab8e498f49\\transformed\\credentials-1.2.0-rc01\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,270", "endColumns": "214,215", "endOffsets": "265,481"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "5528,5743", "endColumns": "214,215", "endOffsets": "5738,5954"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\561943d2b2d87f776fb7a0e02865f59c\\transformed\\core-1.13.0\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,251,456,657,858,1065,1270,1482", "endColumns": "195,204,200,200,206,204,211,203", "endOffsets": "246,451,652,853,1060,1265,1477,1681"}, "to": {"startLines": "31,32,33,34,35,36,37,43", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "5959,6155,6360,6561,6762,6969,7174,8384", "endColumns": "195,204,200,200,206,204,211,203", "endOffsets": "6150,6355,6556,6757,6964,7169,7381,8583"}}]}]}