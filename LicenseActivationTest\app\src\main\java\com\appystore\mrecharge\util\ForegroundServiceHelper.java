package com.appystore.mrecharge.util;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.util.Log;
import android.widget.Toast;

import androidx.core.content.ContextCompat;

import com.appystore.mrecharge.service.sever;

/**
 * Helper class for managing foreground service operations with Android 15 compatibility
 */
public class ForegroundServiceHelper {
    private static final String TAG = "ForegroundServiceHelper";

    /**
     * Safely start the sever foreground service with proper permission checks
     * @param context The context to start the service from
     * @param inputExtra The notification text
     * @return true if service was started successfully, false otherwise
     */
    public static boolean startSeverService(Context context, String inputExtra) {
        try {
            // Check if context is valid
            if (context == null) {
                Log.e(TAG, "Cannot start service - context is null");
                return false;
            }

            // Check permissions before starting service
            if (!PermissionHelper.hasAllNecessaryPermissions(context)) {
                String missingPermissions = PermissionHelper.getMissingPermissionsDescription(context);
                Log.e(TAG, "Cannot start service - missing permissions: " + missingPermissions);
                
                Android15CompatibilityHelper.logToCrashlytics("Attempted to start service without required permissions: " + missingPermissions);
                
                // Show user-friendly error message
                Android15CompatibilityHelper.showSafeToast(context, 
                    "Missing required permissions. Please grant all permissions.", Toast.LENGTH_LONG);
                
                // Request missing permissions if context is an Activity
                if (context instanceof Activity) {
                    PermissionHelper.requestAllNecessaryPermissions((Activity) context);
                }
                
                return false;
            }
            
            // Additional check for Android 15 foreground service permissions
            if (Android15CompatibilityHelper.isAndroid15OrHigher()) {
                if (!PermissionHelper.hasAllForegroundServicePermissions(context)) {
                    Log.e(TAG, "Cannot start service - missing foreground service permissions on Android 15");
                    
                    Android15CompatibilityHelper.showSafeToast(context, 
                        "Foreground service permissions required for Android 15", Toast.LENGTH_LONG);
                    
                    Android15CompatibilityHelper.logToCrashlytics("Missing foreground service permissions on Android 15");
                    return false;
                }
            }
            
            // Create and configure the service intent
            Intent intent = new Intent(context, sever.class);
            intent.putExtra("inputExtra", inputExtra != null ? inputExtra : "Service is running");
            intent.putExtra("serv", "on");
            
            Log.d(TAG, "Starting foreground service with all required permissions");
            Android15CompatibilityHelper.logToCrashlytics("Starting sever foreground service");
            
            // Start the foreground service
            ContextCompat.startForegroundService(context, intent);
            
            Log.d(TAG, "Foreground service started successfully");
            return true;
            
        } catch (SecurityException e) {
            Log.e(TAG, "SecurityException starting service - permission denied: " + e.getMessage(), e);
            Android15CompatibilityHelper.recordException(e);
            
            Android15CompatibilityHelper.showSafeToast(context, 
                "Permission denied. Please grant all required permissions.", Toast.LENGTH_LONG);
            
            return false;
            
        } catch (IllegalStateException e) {
            Log.e(TAG, "IllegalStateException starting service - app in background?: " + e.getMessage(), e);
            Android15CompatibilityHelper.recordException(e);
            
            Android15CompatibilityHelper.showSafeToast(context, 
                "Cannot start service while app is in background", Toast.LENGTH_LONG);
            
            return false;
            
        } catch (Exception e) {
            Log.e(TAG, "Unexpected error starting service: " + e.getMessage(), e);
            Android15CompatibilityHelper.recordException(e);
            
            Android15CompatibilityHelper.showSafeToast(context, 
                "Failed to start service: " + e.getMessage(), Toast.LENGTH_LONG);
            
            return false;
        }
    }

    /**
     * Safely stop the sever foreground service
     * @param context The context to stop the service from
     * @return true if service was stopped successfully, false otherwise
     */
    public static boolean stopSeverService(Context context) {
        try {
            // Check if context is valid
            if (context == null) {
                Log.e(TAG, "Cannot stop service - context is null");
                return false;
            }

            // Create and configure the service intent for stopping
            Intent intent = new Intent(context, sever.class);
            intent.putExtra("inputExtra", "Service stopping");
            intent.putExtra("serv", "off");
            
            Log.d(TAG, "Stopping foreground service");
            Android15CompatibilityHelper.logToCrashlytics("Stopping sever foreground service");
            
            // Start the service with stop command (service will stop itself)
            ContextCompat.startForegroundService(context, intent);
            
            Log.d(TAG, "Foreground service stop command sent successfully");
            return true;
            
        } catch (Exception e) {
            Log.e(TAG, "Error stopping service: " + e.getMessage(), e);
            Android15CompatibilityHelper.recordException(e);
            
            Android15CompatibilityHelper.showSafeToast(context, 
                "Failed to stop service: " + e.getMessage(), Toast.LENGTH_LONG);
            
            return false;
        }
    }

    /**
     * Check if the sever service is currently running
     * @param context The context to check
     * @return true if service is running, false otherwise
     */
    public static boolean isSeverServiceRunning(Context context) {
        try {
            // This is a simplified check - in a real implementation you might want to
            // use ActivityManager to check running services
            return true; // Placeholder implementation
            
        } catch (Exception e) {
            Log.e(TAG, "Error checking service status: " + e.getMessage(), e);
            Android15CompatibilityHelper.recordException(e);
            return false;
        }
    }

    /**
     * Get a user-friendly error message for common foreground service issues
     * @param context The context
     * @param exception The exception that occurred
     * @return User-friendly error message
     */
    public static String getFriendlyErrorMessage(Context context, Exception exception) {
        if (exception instanceof SecurityException) {
            if (exception.getMessage() != null && exception.getMessage().contains("FOREGROUND_SERVICE")) {
                return "Missing foreground service permissions. Please update the app or grant required permissions.";
            }
            return "Permission denied. Please grant all required permissions in app settings.";
        }
        
        if (exception instanceof IllegalStateException) {
            if (exception.getMessage() != null && exception.getMessage().contains("background")) {
                return "Cannot start service while app is in background. Please open the app first.";
            }
            return "App is in an invalid state. Please restart the app.";
        }
        
        // Generic error message
        return "An unexpected error occurred. Please try again or restart the app.";
    }

    /**
     * Show a detailed error dialog for foreground service issues
     * @param activity The activity to show the dialog from
     * @param exception The exception that occurred
     */
    public static void showDetailedErrorDialog(Activity activity, Exception exception) {
        if (!Android15CompatibilityHelper.isActivitySafe(activity)) {
            return;
        }

        String title = "Service Error";
        String message = getFriendlyErrorMessage(activity, exception);
        
        // Add additional details for debugging
        if (Android15CompatibilityHelper.isAndroid15OrHigher()) {
            message += "\n\nNote: This app requires special permissions on Android 15.";
        }
        
        Android15CompatibilityHelper.showSafeDialog(activity, title, message, "OK", new Runnable() {
            @Override
            public void run() {
                // Optionally redirect to app settings or permission request
                if (exception instanceof SecurityException) {
                    PermissionHelper.requestAllNecessaryPermissions(activity);
                }
            }
        });
    }

    /**
     * Validate that all requirements are met for starting a foreground service
     * @param context The context to validate
     * @return ValidationResult with details about what's missing
     */
    public static ValidationResult validateForegroundServiceRequirements(Context context) {
        ValidationResult result = new ValidationResult();
        
        // Check basic permissions
        if (!PermissionHelper.hasAllBasicPermissions(context)) {
            result.addError("Missing basic app permissions");
            result.setValid(false);
        }
        
        // Check foreground service permissions for Android 15+
        if (Android15CompatibilityHelper.isAndroid15OrHigher()) {
            if (!PermissionHelper.hasAllForegroundServicePermissions(context)) {
                result.addError("Missing foreground service permissions (Android 15 requirement)");
                result.setValid(false);
            }
        }
        
        // Check notification permission
        if (!PermissionHelper.hasNotificationPermission(context)) {
            result.addError("Missing notification permission");
            result.setValid(false);
        }
        
        return result;
    }

    /**
     * Simple validation result class
     */
    public static class ValidationResult {
        private boolean isValid = true;
        private StringBuilder errors = new StringBuilder();
        
        public boolean isValid() {
            return isValid;
        }
        
        public void setValid(boolean valid) {
            isValid = valid;
        }
        
        public void addError(String error) {
            if (errors.length() > 0) {
                errors.append("\n");
            }
            errors.append("• ").append(error);
        }
        
        public String getErrors() {
            return errors.toString();
        }
    }
}
