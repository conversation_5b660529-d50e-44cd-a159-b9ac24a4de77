<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:id="@+id/itemLayout"
    android:background="#ff1b1a1a"
    android:focusable="true"
    android:clickable="true"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="0dp">
    <TextView
        android:textAppearance="?android:attr/textAppearanceMedium"
        android:textColor="#fff4f0f0"
        android:id="@+id/status"
        android:padding="5dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>
    <TextView
        android:textAppearance="?android:attr/textAppearanceMedium"
        android:textColor="#fff8f5f5"
        android:id="@+id/tvPrice"
        android:padding="5dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>
    <TextView
        android:background="#ffffffff"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginBottom="1dp"/>
</LinearLayout>