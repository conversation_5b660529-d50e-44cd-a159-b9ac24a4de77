-- Merging decision tree log ---
manifest
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:2:1-321:12
INJECTED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:2:1-321:12
INJECTED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:2:1-321:12
INJECTED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:2:1-321:12
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\d4534216480be3d5f1fe2914165e6dd8\transformed\firebase-auth-23.2.1\AndroidManifest.xml:17:1-75:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\b4c8334a260b71f191b97485b06ad1d4\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\0ab1bb5e4e864e91e4dd8ebfbd0283b4\transformed\constraintlayout-2.2.1\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\ba060d535cbad29767fc4189cb0b0250\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\b42671bce86d044f55bdf4e61e8361bd\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\878d66ccd6979f25990a942b5651ed7a\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\b6f59ff4ed6d94db3d1663a58acae718\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:17:1-66:12
MERGED from [com.google.firebase:firebase-analytics:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\3c45aafb92b7769ba439c06308acc7a9\transformed\firebase-analytics-22.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\3fa094dc8f4005b848e0b308eba656ac\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:17:1-46:12
MERGED from [com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\98a93d668684a960bab7b3805beee81d\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:17:1-39:12
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\61cdfb3e5b6044c89bb21020b4e07dd1\transformed\play-services-measurement-sdk-22.5.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.14\transforms\8c03f49c2d7a4b0abf4600a2b71db677\transformed\firebase-crashlytics-19.4.4\AndroidManifest.xml:2:1-24:12
MERGED from [androidx.credentials:credentials:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\a8ddf21c5cfcfff775adf0ab8e498f49\transformed\credentials-1.2.0-rc01\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\8dd12166401814958a84a1864c86ce1b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:17:1-44:12
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8b2512062bb9036d84302ff507f29fba\transformed\play-services-auth-20.7.0\AndroidManifest.xml:17:1-40:12
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\e2ccf2d347fbcfc2b9b20e2b02de06c8\transformed\play-services-auth-api-phone-18.0.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b3f8d4f2fa635740972b39dbfb64c7bc\transformed\recaptcha-18.6.1\AndroidManifest.xml:2:1-11:12
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\0c5bc5c1b1bcb66d03cb08dd169638a7\transformed\integrity-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\ade4fa97cb09d6bcf6ee05c790ad8d13\transformed\firebase-appcheck-interop-17.0.0\AndroidManifest.xml:15:1-25:12
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\b38e5d219ad81447128af66d56fc191f\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\493565d3d4690f5a6802e799ea942eae\transformed\firebase-sessions-2.1.2\AndroidManifest.xml:15:1-35:12
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\e0122238bc1242e610e88fa5cc869d16\transformed\firebase-installations-18.0.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2a00c4536c133552601fa75968115f9a\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\ef6119fb565be338df0859ad382c537a\transformed\firebase-common-21.0.0\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.firebase:firebase-installations-interop:17.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\0f519872b6922553e2fd1d73fe213c6c\transformed\firebase-installations-interop-17.2.0\AndroidManifest.xml:15:1-19:12
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\7e8a752d6855331926cd555e76261f95\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d36df0582c541ccc22174ae3c1278abf\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:2:1-13:12
MERGED from [com.google.android.gms:play-services-measurement-impl:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\07d254e7f512326632c813717390360a\transformed\play-services-measurement-impl-22.5.0\AndroidManifest.xml:17:1-32:12
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\b1d84428a8c3e57e3055bfb95ff52949\transformed\play-services-fido-20.1.0\AndroidManifest.xml:2:1-10:12
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:17:1-145:12
MERGED from [androidx.room:room-ktx:2.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d2243d02d4a9dfcefa2a6ae56dbddadd\transformed\room-ktx-2.5.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14\transforms\8af3f128235790e2f4c9fdec975ab3de\transformed\ads-adservices-java-1.1.0-beta11\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14\transforms\0f90a3bf636d390373b4501e1b796823\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9069ffa1734f02d0530e955dae8d9191\transformed\core-ktx-1.13.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\de392e826301cd2a7a1dfc8d25687a87\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\07fb0fc7f017af5a92250a9f8c8cb2fa\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\238e20570178668847b99cb57c407b1f\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\52b5791d7db85f26ddd873d4b6292d4c\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2cff8ecd364a05114cbdc63601fcc2ef\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2be23ed379a01868c05f77b5c0b33f6d\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9adcc12e2a9dcc4a95435156581c92e1\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\f060e881648017954abdc5abae43d928\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\f4e40cb66b2591bc7f67ef95d552f790\transformed\browser-1.4.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea3dac0dc758319c399ee5dcf030b963\transformed\play-services-stats-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\148ecced0ecfc65e8bbad25723dfdace\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\83f877db01d7fe737ed25c175c9b0cc1\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\fc05ed69fd2f384f576aa132955e9334\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a66aeccf75bd4cef6fd336b9ba41b25\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\60d20b17c1ea22cad4cdc359c9314625\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-auth-base:18.0.4] C:\Users\<USER>\.gradle\caches\8.14\transforms\eeca036ba0d3bfcfa27c3af69b925787\transformed\play-services-auth-base-18.0.4\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\812f317bcdb04d011f659e61565a4ab5\transformed\play-services-base-18.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\561943d2b2d87f776fb7a0e02865f59c\transformed\core-1.13.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\cd85211ed7b8ae836ab62cdbad529044\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\8604dfed9ec095afef696a5ea4fa0772\transformed\lifecycle-service-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\c46d78812b95fd28ddbfb400e76e1261\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\a49cb89273e9e3dcf6f2423ad2250bdd\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\1bbedf14cf11c43cf5a68cc6c9ea66fd\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\d549eab7796e6462d77e8bfc8211b37d\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\6ac9a4f860cd0c7a3f9fcabd64b45a26\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\2620a4e1163f7188c77ad78bd74baf71\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\0a02bc4a0af44db6e519f203aff165d5\transformed\datastore-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\4628b670779c53d2ea6b1514bdda9a35\transformed\datastore-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\6b3509e8bea1044a62bf2e6fc43fbb8b\transformed\datastore-preferences-release\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\6b12eb554a240dcdb12c41a861a5fa32\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:2:1-5:12
MERGED from [com.google.firebase:firebase-measurement-connector:20.0.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\3ab90e4e66c80149fdc463b6a1bc4a15\transformed\firebase-measurement-connector-20.0.1\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\f4348db6fefa88b61fa28d60d6fefb79\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\76e814db648bd5d6fe600e70696888f2\transformed\play-services-measurement-sdk-api-22.5.0\AndroidManifest.xml:17:1-30:12
MERGED from [com.google.android.gms:play-services-measurement-base:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\1587a2a622c14043c7e78d1b1381e220\transformed\play-services-measurement-base-22.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\42a8a4ad246f20dee6506de075e12431\transformed\play-services-basement-18.5.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.14\transforms\2986ae8bc16c7d7160a6a472d6cbf4fe\transformed\fragment-1.5.4\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\dcaa003595b87d6b6e554bddec328aeb\transformed\activity-1.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\52ce65e21773457615e5d3a97588d6d3\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\3d165c95efd46834a2227584759de2bb\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c7263e1c10130702e5eff4afa7938ce6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\2e54f13732d4378cd498338e89512b5b\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\6693c7e03f02ac3812d71b4d49999756\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\620d74a92415d4cf8ea49d1653789d05\transformed\room-runtime-2.5.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\341759b48abe23d3b98d1e6f757d08f0\transformed\sqlite-framework-2.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a21045d524a85f0af8b2e87d728456d6\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\0480557e5a2c302189a15145d0c2f862\transformed\firebase-components-18.0.0\AndroidManifest.xml:15:1-20:12
MERGED from [com.google.firebase:firebase-config-interop:16.0.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\7b5c7fe9c8dcdcbd64fbd1b33d15263f\transformed\firebase-config-interop-16.0.1\AndroidManifest.xml:15:1-20:12
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c58fb643ce6a1d64d600ee6230e2f799\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:15:1-31:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5418645e2ab8fa5293341787feee6230\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:15:1-35:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\e509ecc0cfa646f77140e39ee1b60bb0\transformed\firebase-encoders-json-18.0.1\AndroidManifest.xml:15:1-22:12
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c547d97ef74fbf725acd50b14bde9b93\transformed\transport-runtime-3.3.0\AndroidManifest.xml:15:1-39:12
MERGED from [com.google.android.datatransport:transport-api:3.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\106a08fb5e85400284ba88252a25005f\transformed\transport-api-3.2.0\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8fd13774f73b0512498cef8e718b03ff\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\bcf57e33e8bc7734f2169cc66ad86f16\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.sqlite:sqlite:2.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\6f07630c7ce48718c90fe0e63aa127ee\transformed\sqlite-2.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\4525fdbb808d03c8bae1591f289f381d\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\49842b9ab5b089c6a5e208b7c0dd2cfd\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2ae09c03cd05209922f95ad4dd3090bd\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.android.volley:volley:1.2.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\8d13967b2e519509cd49a0ff339fddc6\transformed\volley-1.2.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a6c6a4a97d1a22a66eb3404f31facaa2\transformed\core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c6d1400cd62ac67f1eecf847f45b5fc0\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9b0482cde37c31cca78dbab914e959ec\transformed\googleid-1.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\3f6b128159a1ce82a882c0b825dba2e7\transformed\core-common-2.0.3\AndroidManifest.xml:2:1-21:12
	package
		INJECTED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:2:11-69
uses-feature#android.hardware.telephony
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:5:5-7:36
	android:required
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:7:9-33
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:6:9-50
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:9:5-80
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:5-81
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:5-81
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:9:22-78
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:10:5-78
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\d4534216480be3d5f1fe2914165e6dd8\transformed\firebase-auth-23.2.1\AndroidManifest.xml:26:5-79
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\d4534216480be3d5f1fe2914165e6dd8\transformed\firebase-auth-23.2.1\AndroidManifest.xml:26:5-79
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\b6f59ff4ed6d94db3d1663a58acae718\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\b6f59ff4ed6d94db3d1663a58acae718\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\3fa094dc8f4005b848e0b308eba656ac\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\3fa094dc8f4005b848e0b308eba656ac\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\98a93d668684a960bab7b3805beee81d\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\98a93d668684a960bab7b3805beee81d\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b3f8d4f2fa635740972b39dbfb64c7bc\transformed\recaptcha-18.6.1\AndroidManifest.xml:8:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b3f8d4f2fa635740972b39dbfb64c7bc\transformed\recaptcha-18.6.1\AndroidManifest.xml:8:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\e0122238bc1242e610e88fa5cc869d16\transformed\firebase-installations-18.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\e0122238bc1242e610e88fa5cc869d16\transformed\firebase-installations-18.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d36df0582c541ccc22174ae3c1278abf\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d36df0582c541ccc22174ae3c1278abf\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\07d254e7f512326632c813717390360a\transformed\play-services-measurement-impl-22.5.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\07d254e7f512326632c813717390360a\transformed\play-services-measurement-impl-22.5.0\AndroidManifest.xml:24:5-79
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:24:5-79
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\76e814db648bd5d6fe600e70696888f2\transformed\play-services-measurement-sdk-api-22.5.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\76e814db648bd5d6fe600e70696888f2\transformed\play-services-measurement-sdk-api-22.5.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5418645e2ab8fa5293341787feee6230\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5418645e2ab8fa5293341787feee6230\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c547d97ef74fbf725acd50b14bde9b93\transformed\transport-runtime-3.3.0\AndroidManifest.xml:20:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c547d97ef74fbf725acd50b14bde9b93\transformed\transport-runtime-3.3.0\AndroidManifest.xml:20:5-79
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:10:22-76
uses-permission#android.permission.INTERNET
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:11:5-66
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\d4534216480be3d5f1fe2914165e6dd8\transformed\firebase-auth-23.2.1\AndroidManifest.xml:25:5-67
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\d4534216480be3d5f1fe2914165e6dd8\transformed\firebase-auth-23.2.1\AndroidManifest.xml:25:5-67
MERGED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\3fa094dc8f4005b848e0b308eba656ac\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\3fa094dc8f4005b848e0b308eba656ac\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\98a93d668684a960bab7b3805beee81d\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:22:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\98a93d668684a960bab7b3805beee81d\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:22:5-67
MERGED from [com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.14\transforms\8c03f49c2d7a4b0abf4600a2b71db677\transformed\firebase-crashlytics-19.4.4\AndroidManifest.xml:7:5-67
MERGED from [com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.14\transforms\8c03f49c2d7a4b0abf4600a2b71db677\transformed\firebase-crashlytics-19.4.4\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b3f8d4f2fa635740972b39dbfb64c7bc\transformed\recaptcha-18.6.1\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b3f8d4f2fa635740972b39dbfb64c7bc\transformed\recaptcha-18.6.1\AndroidManifest.xml:7:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\e0122238bc1242e610e88fa5cc869d16\transformed\firebase-installations-18.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\e0122238bc1242e610e88fa5cc869d16\transformed\firebase-installations-18.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d36df0582c541ccc22174ae3c1278abf\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d36df0582c541ccc22174ae3c1278abf\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\07d254e7f512326632c813717390360a\transformed\play-services-measurement-impl-22.5.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\07d254e7f512326632c813717390360a\transformed\play-services-measurement-impl-22.5.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\76e814db648bd5d6fe600e70696888f2\transformed\play-services-measurement-sdk-api-22.5.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\76e814db648bd5d6fe600e70696888f2\transformed\play-services-measurement-sdk-api-22.5.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5418645e2ab8fa5293341787feee6230\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5418645e2ab8fa5293341787feee6230\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:23:5-67
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:11:22-64
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:12:5-75
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:12:22-73
uses-permission#android.permission.READ_SMS
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:13:5-66
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:13:22-64
uses-permission#android.permission.SEND_SMS
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:14:5-66
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:14:22-64
uses-permission#android.permission.RECEIVE_SMS
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:15:5-69
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:15:22-67
uses-permission#android.permission.CALL_PHONE
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:16:5-68
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:16:22-66
uses-permission#android.permission.READ_PHONE_STATE
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:17:5-74
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:17:22-72
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:18:5-76
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:26:5-77
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:26:5-77
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:18:22-74
uses-permission#android.permission.FOREGROUND_SERVICE_DATA_SYNC
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:19:5-86
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:19:22-84
uses-permission#android.permission.WAKE_LOCK
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:20:5-67
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\b6f59ff4ed6d94db3d1663a58acae718\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:24:5-68
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\b6f59ff4ed6d94db3d1663a58acae718\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\3fa094dc8f4005b848e0b308eba656ac\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\3fa094dc8f4005b848e0b308eba656ac\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\98a93d668684a960bab7b3805beee81d\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\98a93d668684a960bab7b3805beee81d\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d36df0582c541ccc22174ae3c1278abf\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d36df0582c541ccc22174ae3c1278abf\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\07d254e7f512326632c813717390360a\transformed\play-services-measurement-impl-22.5.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\07d254e7f512326632c813717390360a\transformed\play-services-measurement-impl-22.5.0\AndroidManifest.xml:25:5-68
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:23:5-68
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:23:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\76e814db648bd5d6fe600e70696888f2\transformed\play-services-measurement-sdk-api-22.5.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\76e814db648bd5d6fe600e70696888f2\transformed\play-services-measurement-sdk-api-22.5.0\AndroidManifest.xml:25:5-68
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:20:22-65
uses-permission#android.permission.DISABLE_KEYGUARD
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:21:5-74
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:21:22-72
uses-permission#android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:22:5-94
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:22:22-92
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:23:5-77
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:23:22-75
uses-permission#com.google.android.c2dm.permission.RECEIVE
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:24:5-81
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\b6f59ff4ed6d94db3d1663a58acae718\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:26:5-82
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\b6f59ff4ed6d94db3d1663a58acae718\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:26:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d36df0582c541ccc22174ae3c1278abf\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:11:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d36df0582c541ccc22174ae3c1278abf\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:11:5-82
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:24:22-79
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:25:5-109
MERGED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\3fa094dc8f4005b848e0b308eba656ac\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\3fa094dc8f4005b848e0b308eba656ac\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\07d254e7f512326632c813717390360a\transformed\play-services-measurement-impl-22.5.0\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\07d254e7f512326632c813717390360a\transformed\play-services-measurement-impl-22.5.0\AndroidManifest.xml:26:5-110
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:25:22-107
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:28:5-76
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\b6f59ff4ed6d94db3d1663a58acae718\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:23:5-77
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\b6f59ff4ed6d94db3d1663a58acae718\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:23:5-77
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:28:22-74
uses-permission#android.permission.USE_EXACT_ALARM
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:29:5-73
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:29:22-71
uses-permission#android.permission.SCHEDULE_EXACT_ALARM
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:30:5-78
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:30:22-76
application
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:36:5-319:19
INJECTED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:36:5-319:19
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\d4534216480be3d5f1fe2914165e6dd8\transformed\firebase-auth-23.2.1\AndroidManifest.xml:28:5-73:19
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\d4534216480be3d5f1fe2914165e6dd8\transformed\firebase-auth-23.2.1\AndroidManifest.xml:28:5-73:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\b4c8334a260b71f191b97485b06ad1d4\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\b4c8334a260b71f191b97485b06ad1d4\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\0ab1bb5e4e864e91e4dd8ebfbd0283b4\transformed\constraintlayout-2.2.1\AndroidManifest.xml:7:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\0ab1bb5e4e864e91e4dd8ebfbd0283b4\transformed\constraintlayout-2.2.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\b6f59ff4ed6d94db3d1663a58acae718\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\b6f59ff4ed6d94db3d1663a58acae718\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-analytics:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\3c45aafb92b7769ba439c06308acc7a9\transformed\firebase-analytics-22.5.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-analytics:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\3c45aafb92b7769ba439c06308acc7a9\transformed\firebase-analytics-22.5.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\3fa094dc8f4005b848e0b308eba656ac\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\3fa094dc8f4005b848e0b308eba656ac\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\98a93d668684a960bab7b3805beee81d\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:29:5-37:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\98a93d668684a960bab7b3805beee81d\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:29:5-37:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\61cdfb3e5b6044c89bb21020b4e07dd1\transformed\play-services-measurement-sdk-22.5.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\61cdfb3e5b6044c89bb21020b4e07dd1\transformed\play-services-measurement-sdk-22.5.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.14\transforms\8c03f49c2d7a4b0abf4600a2b71db677\transformed\firebase-crashlytics-19.4.4\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.14\transforms\8c03f49c2d7a4b0abf4600a2b71db677\transformed\firebase-crashlytics-19.4.4\AndroidManifest.xml:11:5-22:19
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\8dd12166401814958a84a1864c86ce1b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:23:5-42:19
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\8dd12166401814958a84a1864c86ce1b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:23:5-42:19
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8b2512062bb9036d84302ff507f29fba\transformed\play-services-auth-20.7.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8b2512062bb9036d84302ff507f29fba\transformed\play-services-auth-20.7.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\0c5bc5c1b1bcb66d03cb08dd169638a7\transformed\integrity-1.3.0\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\0c5bc5c1b1bcb66d03cb08dd169638a7\transformed\integrity-1.3.0\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\ade4fa97cb09d6bcf6ee05c790ad8d13\transformed\firebase-appcheck-interop-17.0.0\AndroidManifest.xml:23:5-20
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\ade4fa97cb09d6bcf6ee05c790ad8d13\transformed\firebase-appcheck-interop-17.0.0\AndroidManifest.xml:23:5-20
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\b38e5d219ad81447128af66d56fc191f\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\b38e5d219ad81447128af66d56fc191f\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\493565d3d4690f5a6802e799ea942eae\transformed\firebase-sessions-2.1.2\AndroidManifest.xml:21:5-33:19
MERGED from [com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\493565d3d4690f5a6802e799ea942eae\transformed\firebase-sessions-2.1.2\AndroidManifest.xml:21:5-33:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\e0122238bc1242e610e88fa5cc869d16\transformed\firebase-installations-18.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\e0122238bc1242e610e88fa5cc869d16\transformed\firebase-installations-18.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2a00c4536c133552601fa75968115f9a\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2a00c4536c133552601fa75968115f9a\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\ef6119fb565be338df0859ad382c537a\transformed\firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\ef6119fb565be338df0859ad382c537a\transformed\firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\7e8a752d6855331926cd555e76261f95\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\7e8a752d6855331926cd555e76261f95\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-measurement-impl:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\07d254e7f512326632c813717390360a\transformed\play-services-measurement-impl-22.5.0\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-measurement-impl:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\07d254e7f512326632c813717390360a\transformed\play-services-measurement-impl-22.5.0\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\b1d84428a8c3e57e3055bfb95ff52949\transformed\play-services-fido-20.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\b1d84428a8c3e57e3055bfb95ff52949\transformed\play-services-fido-20.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14\transforms\0f90a3bf636d390373b4501e1b796823\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14\transforms\0f90a3bf636d390373b4501e1b796823\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\07fb0fc7f017af5a92250a9f8c8cb2fa\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\07fb0fc7f017af5a92250a9f8c8cb2fa\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea3dac0dc758319c399ee5dcf030b963\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea3dac0dc758319c399ee5dcf030b963\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\812f317bcdb04d011f659e61565a4ab5\transformed\play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\812f317bcdb04d011f659e61565a4ab5\transformed\play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\561943d2b2d87f776fb7a0e02865f59c\transformed\core-1.13.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\561943d2b2d87f776fb7a0e02865f59c\transformed\core-1.13.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\c46d78812b95fd28ddbfb400e76e1261\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\c46d78812b95fd28ddbfb400e76e1261\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\6b12eb554a240dcdb12c41a861a5fa32\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\6b12eb554a240dcdb12c41a861a5fa32\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.firebase:firebase-measurement-connector:20.0.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\3ab90e4e66c80149fdc463b6a1bc4a15\transformed\firebase-measurement-connector-20.0.1\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:20.0.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\3ab90e4e66c80149fdc463b6a1bc4a15\transformed\firebase-measurement-connector-20.0.1\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\f4348db6fefa88b61fa28d60d6fefb79\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\f4348db6fefa88b61fa28d60d6fefb79\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\1587a2a622c14043c7e78d1b1381e220\transformed\play-services-measurement-base-22.5.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\1587a2a622c14043c7e78d1b1381e220\transformed\play-services-measurement-base-22.5.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\42a8a4ad246f20dee6506de075e12431\transformed\play-services-basement-18.5.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\42a8a4ad246f20dee6506de075e12431\transformed\play-services-basement-18.5.0\AndroidManifest.xml:20:5-24:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c7263e1c10130702e5eff4afa7938ce6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c7263e1c10130702e5eff4afa7938ce6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\2e54f13732d4378cd498338e89512b5b\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\2e54f13732d4378cd498338e89512b5b\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\620d74a92415d4cf8ea49d1653789d05\transformed\room-runtime-2.5.0\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\620d74a92415d4cf8ea49d1653789d05\transformed\room-runtime-2.5.0\AndroidManifest.xml:23:5-29:19
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c58fb643ce6a1d64d600ee6230e2f799\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c58fb643ce6a1d64d600ee6230e2f799\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5418645e2ab8fa5293341787feee6230\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:25:5-33:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5418645e2ab8fa5293341787feee6230\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:25:5-33:19
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c547d97ef74fbf725acd50b14bde9b93\transformed\transport-runtime-3.3.0\AndroidManifest.xml:23:5-37:19
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c547d97ef74fbf725acd50b14bde9b93\transformed\transport-runtime-3.3.0\AndroidManifest.xml:23:5-37:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\bcf57e33e8bc7734f2169cc66ad86f16\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\bcf57e33e8bc7734f2169cc66ad86f16\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\3f6b128159a1ce82a882c0b825dba2e7\transformed\core-common-2.0.3\AndroidManifest.xml:11:5-19:19
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\3f6b128159a1ce82a882c0b825dba2e7\transformed\core-common-2.0.3\AndroidManifest.xml:11:5-19:19
	android:extractNativeLibs
		INJECTED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\561943d2b2d87f776fb7a0e02865f59c\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:43:9-35
	android:label
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:41:9-41
	android:fullBackupContent
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:39:9-54
	android:roundIcon
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:42:9-48
	tools:targetApi
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:47:9-29
	android:icon
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:40:9-43
	android:allowBackup
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:37:9-35
	android:theme
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:44:9-56
	android:networkSecurityConfig
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:45:9-69
	android:dataExtractionRules
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:38:9-65
	android:usesCleartextTraffic
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:46:9-44
uses-library#org.apache.http.legacy
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:48:9-50:39
	android:required
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:50:13-37
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:49:13-50
activity#com.appystore.mrecharge.activity.MainActivity
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:51:9-59:20
	android:label
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:53:13-45
	android:exported
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:52:13-36
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:54:13-73
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:55:13-58:29
action#android.intent.action.MAIN
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:56:17-68
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:56:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:57:17-76
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:57:27-74
receiver#com.appystore.mrecharge.IncomingSms
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:60:9-65:20
	android:exported
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:61:13-36
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:60:19-69
intent-filter#action:name:android.provider.Telephony.SMS_RECEIVED
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:62:13-64:29
	android:priority
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:62:28-51
action#android.provider.Telephony.SMS_RECEIVED
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:63:17-81
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:63:25-79
service#com.appystore.mrecharge.service.PushService
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:66:9-71:19
	android:exported
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:67:13-37
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:66:18-76
intent-filter#action:name:com.google.firebase.MESSAGING_EVENT
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:68:13-70:29
	android:priority
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:269:28-51
action#com.google.firebase.MESSAGING_EVENT
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:69:17-77
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:69:25-75
service#com.appystore.mrecharge.service.Recharge
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:72:9-75
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:72:18-73
service#com.appystore.mrecharge.service.sever
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:73:9-78:55
	android:enabled
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:75:13-35
	android:exported
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:76:13-36
	android:stopWithTask
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:77:13-41
	android:foregroundServiceType
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:78:13-53
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:74:13-65
service#com.appystore.mrecharge.service.Smsend
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:79:9-73
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:79:18-71
service#com.appystore.mrecharge.service.International_service
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:80:9-88
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:80:18-86
service#com.appystore.mrecharge.service.USSDService
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:81:9-91:19
	android:exported
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:84:13-36
	android:permission
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:83:13-79
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:82:13-71
intent-filter#action:name:android.accessibilityservice.AccessibilityService
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:85:13-87:29
action#android.accessibilityservice.AccessibilityService
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:86:17-91
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:86:25-89
meta-data#android.accessibilityservice
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:88:13-90:55
	android:resource
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:90:17-53
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:89:17-60
activity#com.appystore.mrecharge.activity.Settings
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:92:9-102
	android:exported
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:92:76-100
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:92:19-75
activity#com.appystore.mrecharge.activity.intsetting
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:93:9-104
	android:exported
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:93:78-102
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:93:19-77
activity#com.appystore.mrecharge.activity.Monitoring
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:94:9-104
	android:exported
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:94:78-102
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:94:19-77
provider#androidx.startup.InitializationProvider
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:95:9-102:20
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\07fb0fc7f017af5a92250a9f8c8cb2fa\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\07fb0fc7f017af5a92250a9f8c8cb2fa\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\c46d78812b95fd28ddbfb400e76e1261\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\c46d78812b95fd28ddbfb400e76e1261\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c7263e1c10130702e5eff4afa7938ce6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c7263e1c10130702e5eff4afa7938ce6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\2e54f13732d4378cd498338e89512b5b\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\2e54f13732d4378cd498338e89512b5b\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	android:authorities
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:98:13-75
	android:exported
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:97:13-37
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:96:13-67
meta-data#androidx.work.WorkManagerInitializer
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:99:13-101:51
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:34:13-36:52
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:34:13-36:52
	android:value
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:101:17-49
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:100:17-68
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:103:9-107:46
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:39:9-45:35
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:39:9-45:35
	android:enabled
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:105:13-72
	android:exported
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:106:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:44:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:45:13-32
	android:directBootAware
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:107:13-44
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:104:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:108:9-113:46
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:46:9-52:35
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:46:9-52:35
	android:enabled
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:111:13-70
	android:exported
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:112:13-36
	android:permission
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:110:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:52:13-32
	android:directBootAware
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:113:13-44
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:109:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:114:9-118:46
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:53:9-59:35
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:53:9-59:35
	android:enabled
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:116:13-77
	android:exported
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:117:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:58:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:118:13-44
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:115:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable.BroadcastReceiver
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:119:9-123:46
	android:enabled
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:121:13-35
	android:exported
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:122:13-37
	android:directBootAware
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:123:13-44
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:120:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy.BatteryChargingProxy
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:124:9-133:20
	android:enabled
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:126:13-36
	android:exported
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:127:13-37
	android:directBootAware
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:128:13-44
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:125:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:129:13-132:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:130:17-86
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:130:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:131:17-89
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:131:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy.BatteryNotLowProxy
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:134:9-143:20
	android:enabled
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:136:13-36
	android:exported
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:137:13-37
	android:directBootAware
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:138:13-44
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:135:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:139:13-142:29
action#android.intent.action.BATTERY_OKAY
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:140:17-76
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:140:25-74
action#android.intent.action.BATTERY_LOW
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:141:17-75
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:141:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy.StorageNotLowProxy
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:144:9-153:20
	android:enabled
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:146:13-36
	android:exported
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:147:13-37
	android:directBootAware
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:148:13-44
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:145:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:149:13-152:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:150:17-82
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:150:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:151:17-81
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:151:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy.NetworkStateProxy
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:154:9-162:20
	android:enabled
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:156:13-36
	android:exported
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:157:13-37
	android:directBootAware
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:158:13-44
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:155:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:159:13-161:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:160:17-78
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:160:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:163:9-173:20
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:110:9-121:20
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:165:13-36
	android:exported
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:166:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:167:13-44
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:164:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:168:13-172:29
action#android.intent.action.BOOT_COMPLETED
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:169:17-78
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:169:25-76
action#android.intent.action.TIME_SET
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:170:17-72
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:170:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:171:17-80
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:171:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:174:9-182:20
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:122:9-131:20
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:176:13-72
	android:exported
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:177:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:178:13-44
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:175:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:179:13-181:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:180:17-97
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:180:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:183:9-192:20
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:132:9-142:20
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:186:13-35
	android:exported
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:187:13-36
	android:permission
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:185:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:188:13-44
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:184:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:189:13-191:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:190:17-87
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:190:25-85
activity#com.google.firebase.auth.internal.GenericIdpActivity
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:193:9-208:20
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\d4534216480be3d5f1fe2914165e6dd8\transformed\firebase-auth-23.2.1\AndroidManifest.xml:29:9-46:20
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\d4534216480be3d5f1fe2914165e6dd8\transformed\firebase-auth-23.2.1\AndroidManifest.xml:29:9-46:20
	android:excludeFromRecents
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:197:13-46
	android:launchMode
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:198:13-44
	android:exported
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:196:13-36
	android:theme
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:194:13-72
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:195:13-80
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:genericidp
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:199:13-207:29
action#android.intent.action.VIEW
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:200:17-68
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:200:25-66
category#android.intent.category.DEFAULT
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:201:17-75
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:201:27-73
category#android.intent.category.BROWSABLE
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:202:17-77
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:202:27-75
data
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:203:17-206:39
	android:path
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:206:21-37
	android:host
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:205:21-49
	android:scheme
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:204:21-48
activity#com.google.firebase.auth.internal.RecaptchaActivity
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:209:9-224:20
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\d4534216480be3d5f1fe2914165e6dd8\transformed\firebase-auth-23.2.1\AndroidManifest.xml:47:9-64:20
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\d4534216480be3d5f1fe2914165e6dd8\transformed\firebase-auth-23.2.1\AndroidManifest.xml:47:9-64:20
	android:excludeFromRecents
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:213:13-46
	android:launchMode
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:214:13-44
	android:exported
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:212:13-36
	android:theme
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:210:13-72
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:211:13-79
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:recaptcha
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:215:13-223:29
service#com.google.firebase.auth.api.fallback.service.FirebaseAuthFallbackService
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:225:9-233:19
	android:enabled
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:227:13-35
	android:exported
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:228:13-37
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:226:13-101
intent-filter#action:name:com.google.firebase.auth.api.gms.service.START+category:name:android.intent.category.DEFAULT
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:229:13-232:29
action#com.google.firebase.auth.api.gms.service.START
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:230:17-88
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:230:25-86
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:234:9-256:19
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\d4534216480be3d5f1fe2914165e6dd8\transformed\firebase-auth-23.2.1\AndroidManifest.xml:66:9-72:19
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\d4534216480be3d5f1fe2914165e6dd8\transformed\firebase-auth-23.2.1\AndroidManifest.xml:66:9-72:19
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\b6f59ff4ed6d94db3d1663a58acae718\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\b6f59ff4ed6d94db3d1663a58acae718\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:54:9-63:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\98a93d668684a960bab7b3805beee81d\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:30:9-36:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\98a93d668684a960bab7b3805beee81d\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:30:9-36:19
MERGED from [com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.14\transforms\8c03f49c2d7a4b0abf4600a2b71db677\transformed\firebase-crashlytics-19.4.4\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.14\transforms\8c03f49c2d7a4b0abf4600a2b71db677\transformed\firebase-crashlytics-19.4.4\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\493565d3d4690f5a6802e799ea942eae\transformed\firebase-sessions-2.1.2\AndroidManifest.xml:26:9-32:19
MERGED from [com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\493565d3d4690f5a6802e799ea942eae\transformed\firebase-sessions-2.1.2\AndroidManifest.xml:26:9-32:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\e0122238bc1242e610e88fa5cc869d16\transformed\firebase-installations-18.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\e0122238bc1242e610e88fa5cc869d16\transformed\firebase-installations-18.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2a00c4536c133552601fa75968115f9a\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2a00c4536c133552601fa75968115f9a\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\ef6119fb565be338df0859ad382c537a\transformed\firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\ef6119fb565be338df0859ad382c537a\transformed\firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c58fb643ce6a1d64d600ee6230e2f799\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:22:9-28:19
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c58fb643ce6a1d64d600ee6230e2f799\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:22:9-28:19
	android:exported
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:236:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\ef6119fb565be338df0859ad382c537a\transformed\firebase-common-21.0.0\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:237:13-43
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:235:13-84
meta-data#com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:238:13-240:84
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\d4534216480be3d5f1fe2914165e6dd8\transformed\firebase-auth-23.2.1\AndroidManifest.xml:69:13-71:85
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\d4534216480be3d5f1fe2914165e6dd8\transformed\firebase-auth-23.2.1\AndroidManifest.xml:69:13-71:85
	android:value
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:240:17-82
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:239:17-109
meta-data#com.google.firebase.components:com.google.firebase.crashlytics.CrashlyticsRegistrar
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:241:13-243:84
MERGED from [com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.14\transforms\8c03f49c2d7a4b0abf4600a2b71db677\transformed\firebase-crashlytics-19.4.4\AndroidManifest.xml:18:13-20:85
MERGED from [com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.14\transforms\8c03f49c2d7a4b0abf4600a2b71db677\transformed\firebase-crashlytics-19.4.4\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:243:17-82
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:242:17-115
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:244:13-246:84
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\b6f59ff4ed6d94db3d1663a58acae718\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:60:13-62:85
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\b6f59ff4ed6d94db3d1663a58acae718\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:60:13-62:85
	android:value
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:246:17-82
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:245:17-119
meta-data#com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:247:13-249:84
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c58fb643ce6a1d64d600ee6230e2f799\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:25:13-27:85
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c58fb643ce6a1d64d600ee6230e2f799\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:25:13-27:85
	android:value
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:249:17-82
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:248:17-115
meta-data#com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:250:13-252:84
MERGED from [com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\98a93d668684a960bab7b3805beee81d\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:33:13-35:85
MERGED from [com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\98a93d668684a960bab7b3805beee81d\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:33:13-35:85
	android:value
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:252:17-82
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:251:17-139
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:253:13-255:84
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\e0122238bc1242e610e88fa5cc869d16\transformed\firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\e0122238bc1242e610e88fa5cc869d16\transformed\firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:255:17-82
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:254:17-127
receiver#com.google.firebase.iid.FirebaseInstanceIdReceiver
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:257:9-264:20
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\b6f59ff4ed6d94db3d1663a58acae718\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:29:9-40:20
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\b6f59ff4ed6d94db3d1663a58acae718\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:29:9-40:20
	android:exported
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:260:13-36
	android:permission
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:259:13-73
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:258:13-78
intent-filter#action:name:com.google.android.c2dm.intent.RECEIVE
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:261:13-263:29
action#com.google.android.c2dm.intent.RECEIVE
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:262:17-80
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:262:25-78
service#com.google.firebase.messaging.FirebaseMessagingService
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:265:9-272:19
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\b6f59ff4ed6d94db3d1663a58acae718\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:46:9-53:19
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\b6f59ff4ed6d94db3d1663a58acae718\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:46:9-53:19
	android:exported
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:267:13-37
	android:directBootAware
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:268:13-43
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:266:13-82
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:273:9-279:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5418645e2ab8fa5293341787feee6230\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:26:9-32:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5418645e2ab8fa5293341787feee6230\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:26:9-32:19
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c547d97ef74fbf725acd50b14bde9b93\transformed\transport-runtime-3.3.0\AndroidManifest.xml:34:9-36:40
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c547d97ef74fbf725acd50b14bde9b93\transformed\transport-runtime-3.3.0\AndroidManifest.xml:34:9-36:40
	android:exported
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:275:13-37
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:274:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:276:13-278:38
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5418645e2ab8fa5293341787feee6230\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:29:13-31:39
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5418645e2ab8fa5293341787feee6230\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:29:13-31:39
	android:value
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:278:17-36
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:277:17-94
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:280:9-285:45
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\ef6119fb565be338df0859ad382c537a\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\ef6119fb565be338df0859ad382c537a\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:283:13-79
	android:exported
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:282:13-37
	android:directBootAware
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:285:13-43
	android:initOrder
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:284:13-36
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:281:13-77
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:286:9-289:39
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\812f317bcdb04d011f659e61565a4ab5\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\812f317bcdb04d011f659e61565a4ab5\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
	android:exported
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:289:13-37
	android:theme
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:287:13-72
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:288:13-79
receiver#com.google.android.gms.measurement.AppMeasurementReceiver
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:290:9-293:39
MERGED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\3fa094dc8f4005b848e0b308eba656ac\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:29:9-33:20
MERGED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\3fa094dc8f4005b848e0b308eba656ac\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:29:9-33:20
	android:enabled
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:292:13-35
	android:exported
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:293:13-37
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:291:13-85
service#com.google.android.gms.measurement.AppMeasurementService
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:294:9-297:39
MERGED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\3fa094dc8f4005b848e0b308eba656ac\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:35:9-38:40
MERGED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\3fa094dc8f4005b848e0b308eba656ac\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:35:9-38:40
	android:enabled
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:296:13-35
	android:exported
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:297:13-37
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:295:13-84
service#com.google.android.gms.measurement.AppMeasurementJobService
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:298:9-302:39
MERGED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\3fa094dc8f4005b848e0b308eba656ac\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:39:9-43:72
MERGED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\3fa094dc8f4005b848e0b308eba656ac\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:39:9-43:72
	android:enabled
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:301:13-35
	android:exported
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:302:13-37
	android:permission
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:300:13-69
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:299:13-87
meta-data#com.google.android.gms.version
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:303:9-305:68
MERGED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\42a8a4ad246f20dee6506de075e12431\transformed\play-services-basement-18.5.0\AndroidManifest.xml:21:9-23:69
MERGED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\42a8a4ad246f20dee6506de075e12431\transformed\play-services-basement-18.5.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:305:13-66
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:304:13-58
service#androidx.room.MultiInstanceInvalidationService
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:306:9-309:45
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\620d74a92415d4cf8ea49d1653789d05\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\620d74a92415d4cf8ea49d1653789d05\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:308:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\620d74a92415d4cf8ea49d1653789d05\transformed\room-runtime-2.5.0\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:309:13-43
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:307:13-74
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:310:9-313:39
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c547d97ef74fbf725acd50b14bde9b93\transformed\transport-runtime-3.3.0\AndroidManifest.xml:24:9-28:19
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c547d97ef74fbf725acd50b14bde9b93\transformed\transport-runtime-3.3.0\AndroidManifest.xml:24:9-28:19
	android:exported
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:313:13-37
	android:permission
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:312:13-69
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:311:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:314:9-316:39
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c547d97ef74fbf725acd50b14bde9b93\transformed\transport-runtime-3.3.0\AndroidManifest.xml:30:9-32:40
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c547d97ef74fbf725acd50b14bde9b93\transformed\transport-runtime-3.3.0\AndroidManifest.xml:30:9-32:40
	android:exported
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:316:13-37
	android:name
		ADDED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:315:13-132
uses-sdk
INJECTED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml
INJECTED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\d4534216480be3d5f1fe2914165e6dd8\transformed\firebase-auth-23.2.1\AndroidManifest.xml:21:5-23:151
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\d4534216480be3d5f1fe2914165e6dd8\transformed\firebase-auth-23.2.1\AndroidManifest.xml:21:5-23:151
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\b4c8334a260b71f191b97485b06ad1d4\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\b4c8334a260b71f191b97485b06ad1d4\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\0ab1bb5e4e864e91e4dd8ebfbd0283b4\transformed\constraintlayout-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\0ab1bb5e4e864e91e4dd8ebfbd0283b4\transformed\constraintlayout-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\ba060d535cbad29767fc4189cb0b0250\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\ba060d535cbad29767fc4189cb0b0250\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\b42671bce86d044f55bdf4e61e8361bd\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\b42671bce86d044f55bdf4e61e8361bd\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\878d66ccd6979f25990a942b5651ed7a\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\878d66ccd6979f25990a942b5651ed7a\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\b6f59ff4ed6d94db3d1663a58acae718\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\b6f59ff4ed6d94db3d1663a58acae718\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-analytics:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\3c45aafb92b7769ba439c06308acc7a9\transformed\firebase-analytics-22.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\3c45aafb92b7769ba439c06308acc7a9\transformed\firebase-analytics-22.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\3fa094dc8f4005b848e0b308eba656ac\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\3fa094dc8f4005b848e0b308eba656ac\transformed\play-services-measurement-22.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\98a93d668684a960bab7b3805beee81d\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\98a93d668684a960bab7b3805beee81d\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\61cdfb3e5b6044c89bb21020b4e07dd1\transformed\play-services-measurement-sdk-22.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\61cdfb3e5b6044c89bb21020b4e07dd1\transformed\play-services-measurement-sdk-22.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.14\transforms\8c03f49c2d7a4b0abf4600a2b71db677\transformed\firebase-crashlytics-19.4.4\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.14\transforms\8c03f49c2d7a4b0abf4600a2b71db677\transformed\firebase-crashlytics-19.4.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.credentials:credentials:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\a8ddf21c5cfcfff775adf0ab8e498f49\transformed\credentials-1.2.0-rc01\AndroidManifest.xml:5:5-44
MERGED from [androidx.credentials:credentials:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\a8ddf21c5cfcfff775adf0ab8e498f49\transformed\credentials-1.2.0-rc01\AndroidManifest.xml:5:5-44
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\8dd12166401814958a84a1864c86ce1b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:21:5-44
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\8dd12166401814958a84a1864c86ce1b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8b2512062bb9036d84302ff507f29fba\transformed\play-services-auth-20.7.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8b2512062bb9036d84302ff507f29fba\transformed\play-services-auth-20.7.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\e2ccf2d347fbcfc2b9b20e2b02de06c8\transformed\play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\e2ccf2d347fbcfc2b9b20e2b02de06c8\transformed\play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b3f8d4f2fa635740972b39dbfb64c7bc\transformed\recaptcha-18.6.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b3f8d4f2fa635740972b39dbfb64c7bc\transformed\recaptcha-18.6.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\0c5bc5c1b1bcb66d03cb08dd169638a7\transformed\integrity-1.3.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\0c5bc5c1b1bcb66d03cb08dd169638a7\transformed\integrity-1.3.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\ade4fa97cb09d6bcf6ee05c790ad8d13\transformed\firebase-appcheck-interop-17.0.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\ade4fa97cb09d6bcf6ee05c790ad8d13\transformed\firebase-appcheck-interop-17.0.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\b38e5d219ad81447128af66d56fc191f\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\b38e5d219ad81447128af66d56fc191f\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\493565d3d4690f5a6802e799ea942eae\transformed\firebase-sessions-2.1.2\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\493565d3d4690f5a6802e799ea942eae\transformed\firebase-sessions-2.1.2\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\e0122238bc1242e610e88fa5cc869d16\transformed\firebase-installations-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\e0122238bc1242e610e88fa5cc869d16\transformed\firebase-installations-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2a00c4536c133552601fa75968115f9a\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2a00c4536c133552601fa75968115f9a\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\ef6119fb565be338df0859ad382c537a\transformed\firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\ef6119fb565be338df0859ad382c537a\transformed\firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\0f519872b6922553e2fd1d73fe213c6c\transformed\firebase-installations-interop-17.2.0\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\0f519872b6922553e2fd1d73fe213c6c\transformed\firebase-installations-interop-17.2.0\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\7e8a752d6855331926cd555e76261f95\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\7e8a752d6855331926cd555e76261f95\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d36df0582c541ccc22174ae3c1278abf\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d36df0582c541ccc22174ae3c1278abf\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\07d254e7f512326632c813717390360a\transformed\play-services-measurement-impl-22.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\07d254e7f512326632c813717390360a\transformed\play-services-measurement-impl-22.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\b1d84428a8c3e57e3055bfb95ff52949\transformed\play-services-fido-20.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\b1d84428a8c3e57e3055bfb95ff52949\transformed\play-services-fido-20.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d2243d02d4a9dfcefa2a6ae56dbddadd\transformed\room-ktx-2.5.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\d2243d02d4a9dfcefa2a6ae56dbddadd\transformed\room-ktx-2.5.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14\transforms\8af3f128235790e2f4c9fdec975ab3de\transformed\ads-adservices-java-1.1.0-beta11\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14\transforms\8af3f128235790e2f4c9fdec975ab3de\transformed\ads-adservices-java-1.1.0-beta11\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14\transforms\0f90a3bf636d390373b4501e1b796823\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14\transforms\0f90a3bf636d390373b4501e1b796823\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9069ffa1734f02d0530e955dae8d9191\transformed\core-ktx-1.13.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9069ffa1734f02d0530e955dae8d9191\transformed\core-ktx-1.13.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\de392e826301cd2a7a1dfc8d25687a87\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\de392e826301cd2a7a1dfc8d25687a87\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\07fb0fc7f017af5a92250a9f8c8cb2fa\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\07fb0fc7f017af5a92250a9f8c8cb2fa\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\238e20570178668847b99cb57c407b1f\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\238e20570178668847b99cb57c407b1f\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\52b5791d7db85f26ddd873d4b6292d4c\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\52b5791d7db85f26ddd873d4b6292d4c\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2cff8ecd364a05114cbdc63601fcc2ef\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2cff8ecd364a05114cbdc63601fcc2ef\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2be23ed379a01868c05f77b5c0b33f6d\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2be23ed379a01868c05f77b5c0b33f6d\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9adcc12e2a9dcc4a95435156581c92e1\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9adcc12e2a9dcc4a95435156581c92e1\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\f060e881648017954abdc5abae43d928\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\f060e881648017954abdc5abae43d928\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\f4e40cb66b2591bc7f67ef95d552f790\transformed\browser-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\f4e40cb66b2591bc7f67ef95d552f790\transformed\browser-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea3dac0dc758319c399ee5dcf030b963\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\ea3dac0dc758319c399ee5dcf030b963\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\148ecced0ecfc65e8bbad25723dfdace\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\148ecced0ecfc65e8bbad25723dfdace\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\83f877db01d7fe737ed25c175c9b0cc1\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\83f877db01d7fe737ed25c175c9b0cc1\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\fc05ed69fd2f384f576aa132955e9334\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\fc05ed69fd2f384f576aa132955e9334\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a66aeccf75bd4cef6fd336b9ba41b25\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9a66aeccf75bd4cef6fd336b9ba41b25\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\60d20b17c1ea22cad4cdc359c9314625\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\60d20b17c1ea22cad4cdc359c9314625\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.gms:play-services-auth-base:18.0.4] C:\Users\<USER>\.gradle\caches\8.14\transforms\eeca036ba0d3bfcfa27c3af69b925787\transformed\play-services-auth-base-18.0.4\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.4] C:\Users\<USER>\.gradle\caches\8.14\transforms\eeca036ba0d3bfcfa27c3af69b925787\transformed\play-services-auth-base-18.0.4\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\812f317bcdb04d011f659e61565a4ab5\transformed\play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\812f317bcdb04d011f659e61565a4ab5\transformed\play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\561943d2b2d87f776fb7a0e02865f59c\transformed\core-1.13.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\561943d2b2d87f776fb7a0e02865f59c\transformed\core-1.13.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\cd85211ed7b8ae836ab62cdbad529044\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\cd85211ed7b8ae836ab62cdbad529044\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\8604dfed9ec095afef696a5ea4fa0772\transformed\lifecycle-service-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\8604dfed9ec095afef696a5ea4fa0772\transformed\lifecycle-service-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\c46d78812b95fd28ddbfb400e76e1261\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\c46d78812b95fd28ddbfb400e76e1261\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\a49cb89273e9e3dcf6f2423ad2250bdd\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\a49cb89273e9e3dcf6f2423ad2250bdd\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\1bbedf14cf11c43cf5a68cc6c9ea66fd\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\1bbedf14cf11c43cf5a68cc6c9ea66fd\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\d549eab7796e6462d77e8bfc8211b37d\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\d549eab7796e6462d77e8bfc8211b37d\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\6ac9a4f860cd0c7a3f9fcabd64b45a26\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\6ac9a4f860cd0c7a3f9fcabd64b45a26\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\2620a4e1163f7188c77ad78bd74baf71\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\2620a4e1163f7188c77ad78bd74baf71\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\0a02bc4a0af44db6e519f203aff165d5\transformed\datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\0a02bc4a0af44db6e519f203aff165d5\transformed\datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\4628b670779c53d2ea6b1514bdda9a35\transformed\datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\4628b670779c53d2ea6b1514bdda9a35\transformed\datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\6b3509e8bea1044a62bf2e6fc43fbb8b\transformed\datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\6b3509e8bea1044a62bf2e6fc43fbb8b\transformed\datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\6b12eb554a240dcdb12c41a861a5fa32\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\6b12eb554a240dcdb12c41a861a5fa32\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:20.0.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\3ab90e4e66c80149fdc463b6a1bc4a15\transformed\firebase-measurement-connector-20.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:20.0.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\3ab90e4e66c80149fdc463b6a1bc4a15\transformed\firebase-measurement-connector-20.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\f4348db6fefa88b61fa28d60d6fefb79\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\f4348db6fefa88b61fa28d60d6fefb79\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\76e814db648bd5d6fe600e70696888f2\transformed\play-services-measurement-sdk-api-22.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\76e814db648bd5d6fe600e70696888f2\transformed\play-services-measurement-sdk-api-22.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\1587a2a622c14043c7e78d1b1381e220\transformed\play-services-measurement-base-22.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\1587a2a622c14043c7e78d1b1381e220\transformed\play-services-measurement-base-22.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\42a8a4ad246f20dee6506de075e12431\transformed\play-services-basement-18.5.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\42a8a4ad246f20dee6506de075e12431\transformed\play-services-basement-18.5.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.14\transforms\2986ae8bc16c7d7160a6a472d6cbf4fe\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.14\transforms\2986ae8bc16c7d7160a6a472d6cbf4fe\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\dcaa003595b87d6b6e554bddec328aeb\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\dcaa003595b87d6b6e554bddec328aeb\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\52ce65e21773457615e5d3a97588d6d3\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\52ce65e21773457615e5d3a97588d6d3\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\3d165c95efd46834a2227584759de2bb\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\3d165c95efd46834a2227584759de2bb\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c7263e1c10130702e5eff4afa7938ce6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c7263e1c10130702e5eff4afa7938ce6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\2e54f13732d4378cd498338e89512b5b\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\2e54f13732d4378cd498338e89512b5b\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\6693c7e03f02ac3812d71b4d49999756\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\6693c7e03f02ac3812d71b4d49999756\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\620d74a92415d4cf8ea49d1653789d05\transformed\room-runtime-2.5.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\620d74a92415d4cf8ea49d1653789d05\transformed\room-runtime-2.5.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\341759b48abe23d3b98d1e6f757d08f0\transformed\sqlite-framework-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\341759b48abe23d3b98d1e6f757d08f0\transformed\sqlite-framework-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a21045d524a85f0af8b2e87d728456d6\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a21045d524a85f0af8b2e87d728456d6\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\0480557e5a2c302189a15145d0c2f862\transformed\firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\0480557e5a2c302189a15145d0c2f862\transformed\firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-config-interop:16.0.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\7b5c7fe9c8dcdcbd64fbd1b33d15263f\transformed\firebase-config-interop-16.0.1\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-config-interop:16.0.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\7b5c7fe9c8dcdcbd64fbd1b33d15263f\transformed\firebase-config-interop-16.0.1\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c58fb643ce6a1d64d600ee6230e2f799\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c58fb643ce6a1d64d600ee6230e2f799\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5418645e2ab8fa5293341787feee6230\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\5418645e2ab8fa5293341787feee6230\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-encoders-json:18.0.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\e509ecc0cfa646f77140e39ee1b60bb0\transformed\firebase-encoders-json-18.0.1\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\e509ecc0cfa646f77140e39ee1b60bb0\transformed\firebase-encoders-json-18.0.1\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c547d97ef74fbf725acd50b14bde9b93\transformed\transport-runtime-3.3.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c547d97ef74fbf725acd50b14bde9b93\transformed\transport-runtime-3.3.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\106a08fb5e85400284ba88252a25005f\transformed\transport-api-3.2.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\106a08fb5e85400284ba88252a25005f\transformed\transport-api-3.2.0\AndroidManifest.xml:18:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8fd13774f73b0512498cef8e718b03ff\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8fd13774f73b0512498cef8e718b03ff\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\bcf57e33e8bc7734f2169cc66ad86f16\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\bcf57e33e8bc7734f2169cc66ad86f16\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\6f07630c7ce48718c90fe0e63aa127ee\transformed\sqlite-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\6f07630c7ce48718c90fe0e63aa127ee\transformed\sqlite-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\4525fdbb808d03c8bae1591f289f381d\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\4525fdbb808d03c8bae1591f289f381d\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\49842b9ab5b089c6a5e208b7c0dd2cfd\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\49842b9ab5b089c6a5e208b7c0dd2cfd\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2ae09c03cd05209922f95ad4dd3090bd\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2ae09c03cd05209922f95ad4dd3090bd\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.android.volley:volley:1.2.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\8d13967b2e519509cd49a0ff339fddc6\transformed\volley-1.2.1\AndroidManifest.xml:5:5-43
MERGED from [com.android.volley:volley:1.2.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\8d13967b2e519509cd49a0ff339fddc6\transformed\volley-1.2.1\AndroidManifest.xml:5:5-43
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a6c6a4a97d1a22a66eb3404f31facaa2\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\a6c6a4a97d1a22a66eb3404f31facaa2\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c6d1400cd62ac67f1eecf847f45b5fc0\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c6d1400cd62ac67f1eecf847f45b5fc0\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9b0482cde37c31cca78dbab914e959ec\transformed\googleid-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\9b0482cde37c31cca78dbab914e959ec\transformed\googleid-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\3f6b128159a1ce82a882c0b825dba2e7\transformed\core-common-2.0.3\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\3f6b128159a1ce82a882c0b825dba2e7\transformed\core-common-2.0.3\AndroidManifest.xml:7:5-9:41
	tools:overrideLibrary
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\d4534216480be3d5f1fe2914165e6dd8\transformed\firebase-auth-23.2.1\AndroidManifest.xml:23:9-148
	android:targetSdkVersion
		INJECTED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml
meta-data#com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED
ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\b6f59ff4ed6d94db3d1663a58acae718\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:37:13-39:40
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\b6f59ff4ed6d94db3d1663a58acae718\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:39:17-37
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\b6f59ff4ed6d94db3d1663a58acae718\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:38:17-92
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\b6f59ff4ed6d94db3d1663a58acae718\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:57:13-59:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\b6f59ff4ed6d94db3d1663a58acae718\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:59:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\b6f59ff4ed6d94db3d1663a58acae718\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:58:17-122
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\98a93d668684a960bab7b3805beee81d\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\07d254e7f512326632c813717390360a\transformed\play-services-measurement-impl-22.5.0\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\07d254e7f512326632c813717390360a\transformed\play-services-measurement-impl-22.5.0\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\f4348db6fefa88b61fa28d60d6fefb79\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\f4348db6fefa88b61fa28d60d6fefb79\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\76e814db648bd5d6fe600e70696888f2\transformed\play-services-measurement-sdk-api-22.5.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\76e814db648bd5d6fe600e70696888f2\transformed\play-services-measurement-sdk-api-22.5.0\AndroidManifest.xml:26:5-79
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\98a93d668684a960bab7b3805beee81d\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:25:22-76
uses-permission#android.permission.ACCESS_ADSERVICES_ATTRIBUTION
ADDED from [com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\98a93d668684a960bab7b3805beee81d\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:26:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\76e814db648bd5d6fe600e70696888f2\transformed\play-services-measurement-sdk-api-22.5.0\AndroidManifest.xml:27:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\76e814db648bd5d6fe600e70696888f2\transformed\play-services-measurement-sdk-api-22.5.0\AndroidManifest.xml:27:5-88
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\98a93d668684a960bab7b3805beee81d\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:26:22-85
uses-permission#android.permission.ACCESS_ADSERVICES_AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\98a93d668684a960bab7b3805beee81d\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:27:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\76e814db648bd5d6fe600e70696888f2\transformed\play-services-measurement-sdk-api-22.5.0\AndroidManifest.xml:28:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\76e814db648bd5d6fe600e70696888f2\transformed\play-services-measurement-sdk-api-22.5.0\AndroidManifest.xml:28:5-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\98a93d668684a960bab7b3805beee81d\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:27:22-79
meta-data#com.google.firebase.components:com.google.firebase.crashlytics.FirebaseCrashlyticsKtxRegistrar
ADDED from [com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.14\transforms\8c03f49c2d7a4b0abf4600a2b71db677\transformed\firebase-crashlytics-19.4.4\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.14\transforms\8c03f49c2d7a4b0abf4600a2b71db677\transformed\firebase-crashlytics-19.4.4\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.14\transforms\8c03f49c2d7a4b0abf4600a2b71db677\transformed\firebase-crashlytics-19.4.4\AndroidManifest.xml:16:17-126
service#androidx.credentials.playservices.CredentialProviderMetadataHolder
ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\8dd12166401814958a84a1864c86ce1b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
	android:enabled
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\8dd12166401814958a84a1864c86ce1b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
	android:exported
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\8dd12166401814958a84a1864c86ce1b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\8dd12166401814958a84a1864c86ce1b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:28:13-60
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\8dd12166401814958a84a1864c86ce1b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
meta-data#androidx.credentials.CREDENTIAL_PROVIDER_KEY
ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\8dd12166401814958a84a1864c86ce1b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
	android:value
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\8dd12166401814958a84a1864c86ce1b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\8dd12166401814958a84a1864c86ce1b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
activity#androidx.credentials.playservices.HiddenActivity
ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\8dd12166401814958a84a1864c86ce1b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
	android:fitsSystemWindows
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\8dd12166401814958a84a1864c86ce1b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
	android:enabled
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\8dd12166401814958a84a1864c86ce1b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\8dd12166401814958a84a1864c86ce1b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
	android:configChanges
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\8dd12166401814958a84a1864c86ce1b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
	android:theme
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\8dd12166401814958a84a1864c86ce1b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\8dd12166401814958a84a1864c86ce1b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
activity#com.google.android.gms.auth.api.signin.internal.SignInHubActivity
ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8b2512062bb9036d84302ff507f29fba\transformed\play-services-auth-20.7.0\AndroidManifest.xml:23:9-27:75
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8b2512062bb9036d84302ff507f29fba\transformed\play-services-auth-20.7.0\AndroidManifest.xml:25:13-46
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8b2512062bb9036d84302ff507f29fba\transformed\play-services-auth-20.7.0\AndroidManifest.xml:26:13-37
	android:theme
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8b2512062bb9036d84302ff507f29fba\transformed\play-services-auth-20.7.0\AndroidManifest.xml:27:13-72
	android:name
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8b2512062bb9036d84302ff507f29fba\transformed\play-services-auth-20.7.0\AndroidManifest.xml:24:13-93
service#com.google.android.gms.auth.api.signin.RevocationBoundService
ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8b2512062bb9036d84302ff507f29fba\transformed\play-services-auth-20.7.0\AndroidManifest.xml:33:9-37:51
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8b2512062bb9036d84302ff507f29fba\transformed\play-services-auth-20.7.0\AndroidManifest.xml:35:13-36
	android:visibleToInstantApps
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8b2512062bb9036d84302ff507f29fba\transformed\play-services-auth-20.7.0\AndroidManifest.xml:37:13-48
	android:permission
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8b2512062bb9036d84302ff507f29fba\transformed\play-services-auth-20.7.0\AndroidManifest.xml:36:13-107
	android:name
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8b2512062bb9036d84302ff507f29fba\transformed\play-services-auth-20.7.0\AndroidManifest.xml:34:13-89
uses-permission#com.google.android.providers.gsf.permission.READ_GSERVICES
ADDED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b3f8d4f2fa635740972b39dbfb64c7bc\transformed\recaptcha-18.6.1\AndroidManifest.xml:9:5-98
	android:name
		ADDED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b3f8d4f2fa635740972b39dbfb64c7bc\transformed\recaptcha-18.6.1\AndroidManifest.xml:9:22-95
service#com.google.firebase.sessions.SessionLifecycleService
ADDED from [com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\493565d3d4690f5a6802e799ea942eae\transformed\firebase-sessions-2.1.2\AndroidManifest.xml:22:9-25:40
	android:enabled
		ADDED from [com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\493565d3d4690f5a6802e799ea942eae\transformed\firebase-sessions-2.1.2\AndroidManifest.xml:24:13-35
	android:exported
		ADDED from [com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\493565d3d4690f5a6802e799ea942eae\transformed\firebase-sessions-2.1.2\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\493565d3d4690f5a6802e799ea942eae\transformed\firebase-sessions-2.1.2\AndroidManifest.xml:23:13-80
meta-data#com.google.firebase.components:com.google.firebase.sessions.FirebaseSessionsRegistrar
ADDED from [com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\493565d3d4690f5a6802e799ea942eae\transformed\firebase-sessions-2.1.2\AndroidManifest.xml:29:13-31:85
	android:value
		ADDED from [com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\493565d3d4690f5a6802e799ea942eae\transformed\firebase-sessions-2.1.2\AndroidManifest.xml:31:17-82
	android:name
		ADDED from [com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\493565d3d4690f5a6802e799ea942eae\transformed\firebase-sessions-2.1.2\AndroidManifest.xml:30:17-117
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\e0122238bc1242e610e88fa5cc869d16\transformed\firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\e0122238bc1242e610e88fa5cc869d16\transformed\firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\e0122238bc1242e610e88fa5cc869d16\transformed\firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2a00c4536c133552601fa75968115f9a\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2a00c4536c133552601fa75968115f9a\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2a00c4536c133552601fa75968115f9a\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\ef6119fb565be338df0859ad382c537a\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\ef6119fb565be338df0859ad382c537a\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\ef6119fb565be338df0859ad382c537a\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:68:13-106
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:79:13-104
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:90:13-104
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:101:13-103
uses-library#android.ext.adservices
ADDED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14\transforms\0f90a3bf636d390373b4501e1b796823\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14\transforms\0f90a3bf636d390373b4501e1b796823\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14\transforms\0f90a3bf636d390373b4501e1b796823\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:24:13-50
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\07fb0fc7f017af5a92250a9f8c8cb2fa\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\07fb0fc7f017af5a92250a9f8c8cb2fa\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\07fb0fc7f017af5a92250a9f8c8cb2fa\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\561943d2b2d87f776fb7a0e02865f59c\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\561943d2b2d87f776fb7a0e02865f59c\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\561943d2b2d87f776fb7a0e02865f59c\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
permission#com.appystore.mrecharge.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\561943d2b2d87f776fb7a0e02865f59c\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\561943d2b2d87f776fb7a0e02865f59c\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\561943d2b2d87f776fb7a0e02865f59c\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\561943d2b2d87f776fb7a0e02865f59c\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\561943d2b2d87f776fb7a0e02865f59c\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
uses-permission#com.appystore.mrecharge.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\561943d2b2d87f776fb7a0e02865f59c\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\561943d2b2d87f776fb7a0e02865f59c\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\c46d78812b95fd28ddbfb400e76e1261\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\c46d78812b95fd28ddbfb400e76e1261\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\c46d78812b95fd28ddbfb400e76e1261\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c7263e1c10130702e5eff4afa7938ce6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c7263e1c10130702e5eff4afa7938ce6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c7263e1c10130702e5eff4afa7938ce6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c7263e1c10130702e5eff4afa7938ce6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c7263e1c10130702e5eff4afa7938ce6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c7263e1c10130702e5eff4afa7938ce6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c7263e1c10130702e5eff4afa7938ce6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c7263e1c10130702e5eff4afa7938ce6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c7263e1c10130702e5eff4afa7938ce6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c7263e1c10130702e5eff4afa7938ce6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c7263e1c10130702e5eff4afa7938ce6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c7263e1c10130702e5eff4afa7938ce6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c7263e1c10130702e5eff4afa7938ce6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c7263e1c10130702e5eff4afa7938ce6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c7263e1c10130702e5eff4afa7938ce6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c7263e1c10130702e5eff4afa7938ce6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c7263e1c10130702e5eff4afa7938ce6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c7263e1c10130702e5eff4afa7938ce6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c7263e1c10130702e5eff4afa7938ce6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c7263e1c10130702e5eff4afa7938ce6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c7263e1c10130702e5eff4afa7938ce6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
activity#com.google.android.play.core.common.PlayCoreDialogWrapperActivity
ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\3f6b128159a1ce82a882c0b825dba2e7\transformed\core-common-2.0.3\AndroidManifest.xml:14:9-18:65
	android:stateNotNeeded
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\3f6b128159a1ce82a882c0b825dba2e7\transformed\core-common-2.0.3\AndroidManifest.xml:17:13-42
	android:exported
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\3f6b128159a1ce82a882c0b825dba2e7\transformed\core-common-2.0.3\AndroidManifest.xml:16:13-37
	android:theme
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\3f6b128159a1ce82a882c0b825dba2e7\transformed\core-common-2.0.3\AndroidManifest.xml:18:13-62
	android:name
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\3f6b128159a1ce82a882c0b825dba2e7\transformed\core-common-2.0.3\AndroidManifest.xml:15:13-93
