{"logs": [{"outputFile": "com.appystore.mrecharge.app-mergeReleaseResources-53:/values-night-v8/values-night-v8.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\b4c8334a260b71f191b97485b06ad1d4\\transformed\\material-1.12.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,241,330,431,538,645,744,851,954,1081,1169,1293,1395,1497,1613,1715,1829,1957,2073,2195,2331,2451,2585,2705,2817,2943,3060,3184,3314,3436,3574,3708,3824", "endColumns": "74,110,88,100,106,106,98,106,102,126,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "125,236,325,426,533,640,739,846,949,1076,1164,1288,1390,1492,1608,1710,1824,1952,2068,2190,2326,2446,2580,2700,2812,2938,3055,3179,3309,3431,3569,3703,3819,3939"}, "to": {"startLines": "13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "813,888,999,1088,1189,1296,1403,1502,1609,1712,1839,1927,2051,2153,2255,2371,2473,2587,2715,2831,2953,3089,3209,3343,3463,3575,3790,3907,4031,4161,4283,4421,4555,4671", "endColumns": "74,110,88,100,106,106,98,106,102,126,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "883,994,1083,1184,1291,1398,1497,1604,1707,1834,1922,2046,2148,2250,2366,2468,2582,2710,2826,2948,3084,3204,3338,3458,3570,3696,3902,4026,4156,4278,4416,4550,4666,4786"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14\\transforms\\b42671bce86d044f55bdf4e61e8361bd\\transformed\\appcompat-1.7.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,125,209,293,389,491,593,687", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "120,204,288,384,486,588,682,771"}, "to": {"startLines": "6,7,8,9,10,11,12,39", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "181,251,335,419,515,617,719,3701", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "246,330,414,510,612,714,808,3785"}}, {"source": "C:\\xampp\\htdocs\\AppyStoreMRecharge\\LicenseActivationTest\\app\\src\\main\\res\\values-night\\themes.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "100", "endLines": "5", "endColumns": "12", "endOffsets": "325"}, "to": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "5", "endColumns": "12", "endOffsets": "176"}}]}]}