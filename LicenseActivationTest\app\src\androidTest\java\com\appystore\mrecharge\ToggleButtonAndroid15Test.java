package com.appystore.mrecharge;

import android.content.Context;
import android.content.SharedPreferences;
import android.os.Build;
import android.widget.ToggleButton;

import androidx.test.core.app.ActivityScenario;
import androidx.test.ext.junit.runners.AndroidJUnit4;
import androidx.test.platform.app.InstrumentationRegistry;

import com.appystore.mrecharge.activity.MainActivity;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

import static androidx.test.espresso.Espresso.onView;
import static androidx.test.espresso.action.ViewActions.click;
import static androidx.test.espresso.assertion.ViewAssertions.matches;
import static androidx.test.espresso.matcher.ViewMatchers.isDisplayed;
import static androidx.test.espresso.matcher.ViewMatchers.withId;
import static org.junit.Assert.*;

/**
 * Integration test for ToggleButton functionality on Android 15
 */
@RunWith(AndroidJUnit4.class)
public class ToggleButtonAndroid15Test {

    private Context context;
    private SharedPreferences sharedPreferences;

    @Before
    public void setUp() {
        context = InstrumentationRegistry.getInstrumentation().getTargetContext();
        sharedPreferences = context.getSharedPreferences("test_prefs", Context.MODE_PRIVATE);
        
        // Clear any existing preferences
        sharedPreferences.edit().clear().apply();
    }

    @Test
    public void testToggleButtonExists() {
        try (ActivityScenario<MainActivity> scenario = ActivityScenario.launch(MainActivity.class)) {
            scenario.onActivity(activity -> {
                ToggleButton toggleButton = activity.findViewById(R.id.sav);
                assertNotNull("ToggleButton should exist", toggleButton);
                assertTrue("ToggleButton should be displayed", toggleButton.isShown());
            });
        }
    }

    @Test
    public void testToggleButtonClickWithoutCrash() {
        try (ActivityScenario<MainActivity> scenario = ActivityScenario.launch(MainActivity.class)) {
            // Setup required preferences to avoid validation errors
            setupValidPreferences();
            
            scenario.onActivity(activity -> {
                ToggleButton toggleButton = activity.findViewById(R.id.sav);
                assertNotNull("ToggleButton should exist", toggleButton);
                
                // Test that clicking doesn't crash the app
                try {
                    toggleButton.performClick();
                    // If we reach here, the click didn't crash
                    assertTrue("ToggleButton click should not crash", true);
                } catch (Exception e) {
                    fail("ToggleButton click should not throw exception: " + e.getMessage());
                }
            });
        }
    }

    @Test
    public void testToggleButtonClickHandlerSafety() {
        try (ActivityScenario<MainActivity> scenario = ActivityScenario.launch(MainActivity.class)) {
            setupValidPreferences();
            
            scenario.onActivity(activity -> {
                ToggleButton toggleButton = activity.findViewById(R.id.sav);
                assertNotNull("ToggleButton should exist", toggleButton);
                
                // Test multiple rapid clicks to trigger cooldown mechanism
                for (int i = 0; i < 5; i++) {
                    try {
                        toggleButton.performClick();
                        // Small delay between clicks
                        Thread.sleep(100);
                    } catch (Exception e) {
                        fail("Multiple clicks should not crash: " + e.getMessage());
                    }
                }
            });
        } catch (InterruptedException e) {
            fail("Test interrupted: " + e.getMessage());
        }
    }

    @Test
    public void testToggleButtonWithInvalidPreferences() {
        try (ActivityScenario<MainActivity> scenario = ActivityScenario.launch(MainActivity.class)) {
            // Don't setup valid preferences - test with invalid/missing data
            
            scenario.onActivity(activity -> {
                ToggleButton toggleButton = activity.findViewById(R.id.sav);
                assertNotNull("ToggleButton should exist", toggleButton);
                
                // Test that clicking with invalid preferences doesn't crash
                try {
                    toggleButton.setChecked(true);
                    // If we reach here, the operation didn't crash
                    assertTrue("ToggleButton operation with invalid preferences should not crash", true);
                } catch (Exception e) {
                    fail("ToggleButton operation should handle invalid preferences gracefully: " + e.getMessage());
                }
            });
        }
    }

    @Test
    public void testToggleButtonActivityLifecycleSafety() {
        try (ActivityScenario<MainActivity> scenario = ActivityScenario.launch(MainActivity.class)) {
            setupValidPreferences();
            
            scenario.onActivity(activity -> {
                ToggleButton toggleButton = activity.findViewById(R.id.sav);
                assertNotNull("ToggleButton should exist", toggleButton);
                
                // Test clicking during activity lifecycle changes
                try {
                    toggleButton.performClick();
                } catch (Exception e) {
                    fail("ToggleButton click during lifecycle should not crash: " + e.getMessage());
                }
            });
            
            // Pause and resume activity
            scenario.moveToState(androidx.lifecycle.Lifecycle.State.STARTED);
            scenario.moveToState(androidx.lifecycle.Lifecycle.State.RESUMED);
            
            scenario.onActivity(activity -> {
                ToggleButton toggleButton = activity.findViewById(R.id.sav);
                assertNotNull("ToggleButton should still exist after lifecycle changes", toggleButton);
                
                try {
                    toggleButton.performClick();
                } catch (Exception e) {
                    fail("ToggleButton click after lifecycle changes should not crash: " + e.getMessage());
                }
            });
        }
    }

    @Test
    public void testAndroid15SpecificFeatures() {
        // Only run this test on Android 15+
        if (Build.VERSION.SDK_INT >= 35) { // Android 15 is API level 35
            try (ActivityScenario<MainActivity> scenario = ActivityScenario.launch(MainActivity.class)) {
                setupValidPreferences();
                
                scenario.onActivity(activity -> {
                    ToggleButton toggleButton = activity.findViewById(R.id.sav);
                    assertNotNull("ToggleButton should exist on Android 15", toggleButton);
                    
                    // Test Android 15 specific behavior
                    try {
                        // Test that the new safety checks work
                        toggleButton.performClick();
                        assertTrue("Android 15 safety checks should work", true);
                    } catch (Exception e) {
                        fail("Android 15 specific features should work: " + e.getMessage());
                    }
                });
            }
        }
    }

    @Test
    public void testPermissionHandling() {
        try (ActivityScenario<MainActivity> scenario = ActivityScenario.launch(MainActivity.class)) {
            scenario.onActivity(activity -> {
                // Test that the app handles permission requests gracefully
                try {
                    // This should not crash even if permissions are not granted
                    ToggleButton toggleButton = activity.findViewById(R.id.sav);
                    assertNotNull("ToggleButton should exist even without permissions", toggleButton);
                } catch (Exception e) {
                    fail("Permission handling should not crash: " + e.getMessage());
                }
            });
        }
    }

    @Test
    public void testErrorHandlingInToggleButton() {
        try (ActivityScenario<MainActivity> scenario = ActivityScenario.launch(MainActivity.class)) {
            scenario.onActivity(activity -> {
                ToggleButton toggleButton = activity.findViewById(R.id.sav);
                assertNotNull("ToggleButton should exist", toggleButton);
                
                // Test error handling by setting invalid state
                try {
                    // Force an error condition by setting null values in preferences
                    SharedPreferences prefs = activity.getSharedPreferences("pref", Context.MODE_PRIVATE);
                    prefs.edit().clear().apply();
                    
                    toggleButton.performClick();
                    
                    // The click should be handled gracefully even with invalid data
                    assertTrue("Error handling should prevent crashes", true);
                } catch (Exception e) {
                    fail("Error handling should prevent exceptions: " + e.getMessage());
                }
            });
        }
    }

    /**
     * Setup valid preferences for testing
     */
    private void setupValidPreferences() {
        SharedPreferences prefs = context.getSharedPreferences("pref", Context.MODE_PRIVATE);
        prefs.edit()
            .putString("licence", "test_license_key_1234567890")
            .putString("pin", "1234")
            .putString("myid", "test_device_id")
            .putBoolean("server_on", false)
            .apply();
    }
}
