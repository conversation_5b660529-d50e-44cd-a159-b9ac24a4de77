1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.appystore.mrecharge"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-feature
11-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:5:5-7:36
12        android:name="android.hardware.telephony"
12-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:6:9-50
13        android:required="false" />
13-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:7:9-33
14
15    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
15-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:9:5-80
15-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:9:22-78
16    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
16-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:10:5-78
16-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:10:22-76
17    <uses-permission android:name="android.permission.INTERNET" />
17-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:11:5-66
17-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:11:22-64
18    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
18-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:12:5-75
18-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:12:22-73
19    <uses-permission android:name="android.permission.READ_SMS" />
19-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:13:5-66
19-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:13:22-64
20    <uses-permission android:name="android.permission.SEND_SMS" />
20-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:14:5-66
20-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:14:22-64
21    <uses-permission android:name="android.permission.RECEIVE_SMS" />
21-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:15:5-69
21-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:15:22-67
22    <uses-permission android:name="android.permission.CALL_PHONE" />
22-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:16:5-68
22-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:16:22-66
23    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
23-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:17:5-74
23-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:17:22-72
24    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
24-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:18:5-76
24-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:18:22-74
25    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
25-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:19:5-86
25-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:19:22-84
26    <uses-permission android:name="android.permission.WAKE_LOCK" />
26-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:20:5-67
26-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:20:22-65
27    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
27-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:21:5-74
27-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:21:22-72
28    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
28-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:22:5-94
28-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:22:22-92
29    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
29-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:23:5-77
29-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:23:22-75
30    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
30-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:24:5-81
30-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:24:22-79
31    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
31-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:25:5-109
31-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:25:22-107
32
33    <!-- Android 15 specific permissions -->
34    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
34-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:28:5-76
34-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:28:22-74
35    <uses-permission android:name="android.permission.USE_EXACT_ALARM" />
35-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:29:5-73
35-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:29:22-71
36    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
36-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:30:5-78
36-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:30:22-76
37    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
37-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\98a93d668684a960bab7b3805beee81d\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:25:5-79
37-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\98a93d668684a960bab7b3805beee81d\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:25:22-76
38    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
38-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\98a93d668684a960bab7b3805beee81d\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:26:5-88
38-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\98a93d668684a960bab7b3805beee81d\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:26:22-85
39    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
39-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\98a93d668684a960bab7b3805beee81d\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:27:5-82
39-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\98a93d668684a960bab7b3805beee81d\transformed\play-services-measurement-api-22.5.0\AndroidManifest.xml:27:22-79
40    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
40-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b3f8d4f2fa635740972b39dbfb64c7bc\transformed\recaptcha-18.6.1\AndroidManifest.xml:9:5-98
40-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.14\transforms\b3f8d4f2fa635740972b39dbfb64c7bc\transformed\recaptcha-18.6.1\AndroidManifest.xml:9:22-95
41
42    <permission
42-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\561943d2b2d87f776fb7a0e02865f59c\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
43        android:name="com.appystore.mrecharge.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
43-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\561943d2b2d87f776fb7a0e02865f59c\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
44        android:protectionLevel="signature" />
44-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\561943d2b2d87f776fb7a0e02865f59c\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
45
46    <uses-permission android:name="com.appystore.mrecharge.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" /> <!-- Phone call permissions only if actually making calls -->
46-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\561943d2b2d87f776fb7a0e02865f59c\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
46-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\561943d2b2d87f776fb7a0e02865f59c\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
47    <!-- <uses-permission android:name="android.permission.FOREGROUND_SERVICE_PHONE_CALL"/> -->
48    <!-- <uses-permission android:name="android.permission.MANAGE_OWN_CALLS"/> -->
49    <application
49-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:36:5-319:19
50        android:allowBackup="true"
50-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:37:9-35
51        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
51-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\561943d2b2d87f776fb7a0e02865f59c\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
52        android:dataExtractionRules="@xml/data_extraction_rules"
52-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:38:9-65
53        android:extractNativeLibs="false"
54        android:fullBackupContent="@xml/backup_rules"
54-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:39:9-54
55        android:icon="@mipmap/ic_launcher"
55-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:40:9-43
56        android:label="@string/app_name"
56-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:41:9-41
57        android:networkSecurityConfig="@xml/network_security_config"
57-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:45:9-69
58        android:roundIcon="@mipmap/ic_launcher"
58-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:42:9-48
59        android:supportsRtl="true"
59-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:43:9-35
60        android:theme="@style/Theme.AppyStoreMRecharge"
60-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:44:9-56
61        android:usesCleartextTraffic="true" >
61-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:46:9-44
62        <uses-library
62-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:48:9-50:39
63            android:name="org.apache.http.legacy"
63-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:49:13-50
64            android:required="false" />
64-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:50:13-37
65
66        <activity
66-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:51:9-59:20
67            android:name="com.appystore.mrecharge.activity.MainActivity"
67-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:54:13-73
68            android:exported="true"
68-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:52:13-36
69            android:label="@string/app_name" >
69-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:53:13-45
70            <intent-filter>
70-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:55:13-58:29
71                <action android:name="android.intent.action.MAIN" />
71-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:56:17-68
71-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:56:25-66
72
73                <category android:name="android.intent.category.LAUNCHER" />
73-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:57:17-76
73-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:57:27-74
74            </intent-filter>
75        </activity>
76
77        <receiver
77-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:60:9-65:20
78            android:name="com.appystore.mrecharge.IncomingSms"
78-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:60:19-69
79            android:exported="true" >
79-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:61:13-36
80            <intent-filter android:priority="1000" >
80-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:62:13-64:29
80-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:62:28-51
81                <action android:name="android.provider.Telephony.SMS_RECEIVED" />
81-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:63:17-81
81-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:63:25-79
82            </intent-filter>
83        </receiver>
84
85        <service
85-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:66:9-71:19
86            android:name="com.appystore.mrecharge.service.PushService"
86-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:66:18-76
87            android:exported="false" >
87-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:67:13-37
88            <intent-filter>
88-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:68:13-70:29
89                <action android:name="com.google.firebase.MESSAGING_EVENT" />
89-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:69:17-77
89-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:69:25-75
90            </intent-filter>
91        </service>
92        <service android:name="com.appystore.mrecharge.service.Recharge" />
92-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:72:9-75
92-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:72:18-73
93        <service
93-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:73:9-78:55
94            android:name="com.appystore.mrecharge.service.sever"
94-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:74:13-65
95            android:enabled="true"
95-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:75:13-35
96            android:exported="true"
96-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:76:13-36
97            android:foregroundServiceType="dataSync"
97-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:78:13-53
98            android:stopWithTask="false" />
98-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:77:13-41
99        <service android:name="com.appystore.mrecharge.service.Smsend" />
99-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:79:9-73
99-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:79:18-71
100        <service android:name="com.appystore.mrecharge.service.International_service" />
100-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:80:9-88
100-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:80:18-86
101        <service
101-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:81:9-91:19
102            android:name="com.appystore.mrecharge.service.USSDService"
102-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:82:13-71
103            android:exported="true"
103-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:84:13-36
104            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
104-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:83:13-79
105            <intent-filter>
105-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:85:13-87:29
106                <action android:name="android.accessibilityservice.AccessibilityService" />
106-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:86:17-91
106-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:86:25-89
107            </intent-filter>
108
109            <meta-data
109-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:88:13-90:55
110                android:name="android.accessibilityservice"
110-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:89:17-60
111                android:resource="@xml/ussd_service" />
111-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:90:17-53
112        </service>
113
114        <activity
114-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:92:9-102
115            android:name="com.appystore.mrecharge.activity.Settings"
115-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:92:19-75
116            android:exported="false" />
116-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:92:76-100
117        <activity
117-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:93:9-104
118            android:name="com.appystore.mrecharge.activity.intsetting"
118-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:93:19-77
119            android:exported="false" />
119-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:93:78-102
120        <activity
120-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:94:9-104
121            android:name="com.appystore.mrecharge.activity.Monitoring"
121-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:94:19-77
122            android:exported="false" />
122-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:94:78-102
123
124        <provider
124-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:95:9-102:20
125            android:name="androidx.startup.InitializationProvider"
125-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:96:13-67
126            android:authorities="com.appystore.mrecharge.androidx-startup"
126-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:98:13-75
127            android:exported="false" >
127-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:97:13-37
128            <meta-data
128-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:99:13-101:51
129                android:name="androidx.work.WorkManagerInitializer"
129-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:100:17-68
130                android:value="androidx.startup" />
130-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:101:17-49
131            <meta-data
131-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\07fb0fc7f017af5a92250a9f8c8cb2fa\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
132                android:name="androidx.emoji2.text.EmojiCompatInitializer"
132-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\07fb0fc7f017af5a92250a9f8c8cb2fa\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
133                android:value="androidx.startup" />
133-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\07fb0fc7f017af5a92250a9f8c8cb2fa\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
134            <meta-data
134-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\c46d78812b95fd28ddbfb400e76e1261\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
135                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
135-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\c46d78812b95fd28ddbfb400e76e1261\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
136                android:value="androidx.startup" />
136-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\c46d78812b95fd28ddbfb400e76e1261\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
137            <meta-data
137-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c7263e1c10130702e5eff4afa7938ce6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
138                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
138-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c7263e1c10130702e5eff4afa7938ce6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
139                android:value="androidx.startup" />
139-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c7263e1c10130702e5eff4afa7938ce6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
140        </provider>
141
142        <service
142-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:103:9-107:46
143            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
143-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:104:13-88
144            android:directBootAware="false"
144-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:107:13-44
145            android:enabled="@bool/enable_system_alarm_service_default"
145-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:105:13-72
146            android:exported="false" />
146-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:106:13-37
147        <service
147-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:108:9-113:46
148            android:name="androidx.work.impl.background.systemjob.SystemJobService"
148-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:109:13-84
149            android:directBootAware="false"
149-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:113:13-44
150            android:enabled="@bool/enable_system_job_service_default"
150-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:111:13-70
151            android:exported="true"
151-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:112:13-36
152            android:permission="android.permission.BIND_JOB_SERVICE" />
152-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:110:13-69
153        <service
153-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:114:9-118:46
154            android:name="androidx.work.impl.foreground.SystemForegroundService"
154-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:115:13-81
155            android:directBootAware="false"
155-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:118:13-44
156            android:enabled="@bool/enable_system_foreground_service_default"
156-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:116:13-77
157            android:exported="false" />
157-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:117:13-37
158
159        <receiver
159-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:119:9-123:46
160            android:name="androidx.work.impl.utils.ForceStopRunnable.BroadcastReceiver"
160-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:120:13-88
161            android:directBootAware="false"
161-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:123:13-44
162            android:enabled="true"
162-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:121:13-35
163            android:exported="false" />
163-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:122:13-37
164        <receiver
164-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:124:9-133:20
165            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy.BatteryChargingProxy"
165-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:125:13-106
166            android:directBootAware="false"
166-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:128:13-44
167            android:enabled="false"
167-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:126:13-36
168            android:exported="false" >
168-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:127:13-37
169            <intent-filter>
169-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:129:13-132:29
170                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
170-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:130:17-86
170-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:130:25-84
171                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
171-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:131:17-89
171-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:131:25-87
172            </intent-filter>
173        </receiver>
174        <receiver
174-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:134:9-143:20
175            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy.BatteryNotLowProxy"
175-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:135:13-104
176            android:directBootAware="false"
176-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:138:13-44
177            android:enabled="false"
177-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:136:13-36
178            android:exported="false" >
178-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:137:13-37
179            <intent-filter>
179-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:139:13-142:29
180                <action android:name="android.intent.action.BATTERY_OKAY" />
180-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:140:17-76
180-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:140:25-74
181                <action android:name="android.intent.action.BATTERY_LOW" />
181-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:141:17-75
181-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:141:25-73
182            </intent-filter>
183        </receiver>
184        <receiver
184-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:144:9-153:20
185            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy.StorageNotLowProxy"
185-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:145:13-104
186            android:directBootAware="false"
186-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:148:13-44
187            android:enabled="false"
187-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:146:13-36
188            android:exported="false" >
188-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:147:13-37
189            <intent-filter>
189-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:149:13-152:29
190                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
190-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:150:17-82
190-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:150:25-80
191                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
191-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:151:17-81
191-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:151:25-79
192            </intent-filter>
193        </receiver>
194        <receiver
194-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:154:9-162:20
195            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy.NetworkStateProxy"
195-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:155:13-103
196            android:directBootAware="false"
196-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:158:13-44
197            android:enabled="false"
197-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:156:13-36
198            android:exported="false" >
198-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:157:13-37
199            <intent-filter>
199-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:159:13-161:29
200                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
200-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:160:17-78
200-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:160:25-76
201            </intent-filter>
202        </receiver>
203        <receiver
203-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:163:9-173:20
204            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
204-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:164:13-88
205            android:directBootAware="false"
205-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:167:13-44
206            android:enabled="false"
206-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:165:13-36
207            android:exported="false" >
207-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:166:13-37
208            <intent-filter>
208-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:168:13-172:29
209                <action android:name="android.intent.action.BOOT_COMPLETED" />
209-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:169:17-78
209-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:169:25-76
210                <action android:name="android.intent.action.TIME_SET" />
210-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:170:17-72
210-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:170:25-70
211                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
211-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:171:17-80
211-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:171:25-78
212            </intent-filter>
213        </receiver>
214        <receiver
214-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:174:9-182:20
215            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
215-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:175:13-99
216            android:directBootAware="false"
216-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:178:13-44
217            android:enabled="@bool/enable_system_alarm_service_default"
217-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:176:13-72
218            android:exported="false" >
218-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:177:13-37
219            <intent-filter>
219-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:179:13-181:29
220                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
220-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:180:17-97
220-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:180:25-95
221            </intent-filter>
222        </receiver>
223        <receiver
223-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:183:9-192:20
224            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
224-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:184:13-78
225            android:directBootAware="false"
225-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:188:13-44
226            android:enabled="true"
226-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:186:13-35
227            android:exported="true"
227-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:187:13-36
228            android:permission="android.permission.DUMP" >
228-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:185:13-57
229            <intent-filter>
229-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:189:13-191:29
230                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
230-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:190:17-87
230-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:190:25-85
231            </intent-filter>
232        </receiver>
233
234        <activity
234-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:193:9-208:20
235            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
235-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:195:13-80
236            android:excludeFromRecents="true"
236-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:197:13-46
237            android:exported="true"
237-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:196:13-36
238            android:launchMode="singleTask"
238-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:198:13-44
239            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
239-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:194:13-72
240            <intent-filter>
240-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:199:13-207:29
241                <action android:name="android.intent.action.VIEW" />
241-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:200:17-68
241-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:200:25-66
242
243                <category android:name="android.intent.category.DEFAULT" />
243-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:201:17-75
243-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:201:27-73
244                <category android:name="android.intent.category.BROWSABLE" />
244-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:202:17-77
244-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:202:27-75
245
246                <data
246-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:203:17-206:39
247                    android:host="firebase.auth"
247-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:205:21-49
248                    android:path="/"
248-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:206:21-37
249                    android:scheme="genericidp" />
249-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:204:21-48
250            </intent-filter>
251        </activity>
252        <activity
252-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:209:9-224:20
253            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
253-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:211:13-79
254            android:excludeFromRecents="true"
254-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:213:13-46
255            android:exported="true"
255-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:212:13-36
256            android:launchMode="singleTask"
256-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:214:13-44
257            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
257-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:210:13-72
258            <intent-filter>
258-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:215:13-223:29
259                <action android:name="android.intent.action.VIEW" />
259-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:200:17-68
259-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:200:25-66
260
261                <category android:name="android.intent.category.DEFAULT" />
261-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:201:17-75
261-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:201:27-73
262                <category android:name="android.intent.category.BROWSABLE" />
262-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:202:17-77
262-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:202:27-75
263
264                <data
264-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:203:17-206:39
265                    android:host="firebase.auth"
265-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:205:21-49
266                    android:path="/"
266-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:206:21-37
267                    android:scheme="recaptcha" />
267-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:204:21-48
268            </intent-filter>
269        </activity>
270
271        <service
271-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:225:9-233:19
272            android:name="com.google.firebase.auth.api.fallback.service.FirebaseAuthFallbackService"
272-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:226:13-101
273            android:enabled="true"
273-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:227:13-35
274            android:exported="false" >
274-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:228:13-37
275            <intent-filter>
275-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:229:13-232:29
276                <action android:name="com.google.firebase.auth.api.gms.service.START" />
276-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:230:17-88
276-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:230:25-86
277
278                <category android:name="android.intent.category.DEFAULT" />
278-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:201:17-75
278-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:201:27-73
279            </intent-filter>
280        </service>
281        <service
281-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:234:9-256:19
282            android:name="com.google.firebase.components.ComponentDiscoveryService"
282-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:235:13-84
283            android:directBootAware="true"
283-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:237:13-43
284            android:exported="false" >
284-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:236:13-37
285            <meta-data
285-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:238:13-240:84
286                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
286-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:239:17-109
287                android:value="com.google.firebase.components.ComponentRegistrar" />
287-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:240:17-82
288            <meta-data
288-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:241:13-243:84
289                android:name="com.google.firebase.components:com.google.firebase.crashlytics.CrashlyticsRegistrar"
289-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:242:17-115
290                android:value="com.google.firebase.components.ComponentRegistrar" />
290-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:243:17-82
291            <meta-data
291-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:244:13-246:84
292                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
292-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:245:17-119
293                android:value="com.google.firebase.components.ComponentRegistrar" />
293-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:246:17-82
294            <meta-data
294-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:247:13-249:84
295                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
295-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:248:17-115
296                android:value="com.google.firebase.components.ComponentRegistrar" />
296-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:249:17-82
297            <meta-data
297-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:250:13-252:84
298                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
298-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:251:17-139
299                android:value="com.google.firebase.components.ComponentRegistrar" />
299-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:252:17-82
300            <meta-data
300-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:253:13-255:84
301                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
301-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:254:17-127
302                android:value="com.google.firebase.components.ComponentRegistrar" />
302-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:255:17-82
303            <meta-data
303-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\b6f59ff4ed6d94db3d1663a58acae718\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:57:13-59:85
304                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
304-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\b6f59ff4ed6d94db3d1663a58acae718\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:58:17-122
305                android:value="com.google.firebase.components.ComponentRegistrar" />
305-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\b6f59ff4ed6d94db3d1663a58acae718\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:59:17-82
306            <meta-data
306-->[com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.14\transforms\8c03f49c2d7a4b0abf4600a2b71db677\transformed\firebase-crashlytics-19.4.4\AndroidManifest.xml:15:13-17:85
307                android:name="com.google.firebase.components:com.google.firebase.crashlytics.FirebaseCrashlyticsKtxRegistrar"
307-->[com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.14\transforms\8c03f49c2d7a4b0abf4600a2b71db677\transformed\firebase-crashlytics-19.4.4\AndroidManifest.xml:16:17-126
308                android:value="com.google.firebase.components.ComponentRegistrar" />
308-->[com.google.firebase:firebase-crashlytics:19.4.4] C:\Users\<USER>\.gradle\caches\8.14\transforms\8c03f49c2d7a4b0abf4600a2b71db677\transformed\firebase-crashlytics-19.4.4\AndroidManifest.xml:17:17-82
309            <meta-data
309-->[com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\493565d3d4690f5a6802e799ea942eae\transformed\firebase-sessions-2.1.2\AndroidManifest.xml:29:13-31:85
310                android:name="com.google.firebase.components:com.google.firebase.sessions.FirebaseSessionsRegistrar"
310-->[com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\493565d3d4690f5a6802e799ea942eae\transformed\firebase-sessions-2.1.2\AndroidManifest.xml:30:17-117
311                android:value="com.google.firebase.components.ComponentRegistrar" />
311-->[com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\493565d3d4690f5a6802e799ea942eae\transformed\firebase-sessions-2.1.2\AndroidManifest.xml:31:17-82
312            <meta-data
312-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\e0122238bc1242e610e88fa5cc869d16\transformed\firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
313                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
313-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\e0122238bc1242e610e88fa5cc869d16\transformed\firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
314                android:value="com.google.firebase.components.ComponentRegistrar" />
314-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\e0122238bc1242e610e88fa5cc869d16\transformed\firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
315            <meta-data
315-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2a00c4536c133552601fa75968115f9a\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
316                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
316-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2a00c4536c133552601fa75968115f9a\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
317                android:value="com.google.firebase.components.ComponentRegistrar" />
317-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\2a00c4536c133552601fa75968115f9a\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
318            <meta-data
318-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\ef6119fb565be338df0859ad382c537a\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
319                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
319-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\ef6119fb565be338df0859ad382c537a\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
320                android:value="com.google.firebase.components.ComponentRegistrar" />
320-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\ef6119fb565be338df0859ad382c537a\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
321        </service>
322
323        <receiver
323-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:257:9-264:20
324            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
324-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:258:13-78
325            android:exported="true"
325-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:260:13-36
326            android:permission="com.google.android.c2dm.permission.SEND" >
326-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:259:13-73
327            <intent-filter>
327-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:261:13-263:29
328                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
328-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:262:17-80
328-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:262:25-78
329            </intent-filter>
330
331            <meta-data
331-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\b6f59ff4ed6d94db3d1663a58acae718\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:37:13-39:40
332                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
332-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\b6f59ff4ed6d94db3d1663a58acae718\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:38:17-92
333                android:value="true" />
333-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\b6f59ff4ed6d94db3d1663a58acae718\transformed\firebase-messaging-24.1.2\AndroidManifest.xml:39:17-37
334        </receiver>
335
336        <service
336-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:265:9-272:19
337            android:name="com.google.firebase.messaging.FirebaseMessagingService"
337-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:266:13-82
338            android:directBootAware="true"
338-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:268:13-43
339            android:exported="false" >
339-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:267:13-37
340            <intent-filter android:priority="-500" >
340-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:68:13-70:29
340-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:269:28-51
341                <action android:name="com.google.firebase.MESSAGING_EVENT" />
341-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:69:17-77
341-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:69:25-75
342            </intent-filter>
343        </service>
344        <service
344-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:273:9-279:19
345            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
345-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:274:13-103
346            android:exported="false" >
346-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:275:13-37
347            <meta-data
347-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:276:13-278:38
348                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
348-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:277:17-94
349                android:value="cct" />
349-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:278:17-36
350        </service>
351
352        <provider
352-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:280:9-285:45
353            android:name="com.google.firebase.provider.FirebaseInitProvider"
353-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:281:13-77
354            android:authorities="com.appystore.mrecharge.firebaseinitprovider"
354-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:283:13-79
355            android:directBootAware="true"
355-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:285:13-43
356            android:exported="false"
356-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:282:13-37
357            android:initOrder="100" />
357-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:284:13-36
358
359        <activity
359-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:286:9-289:39
360            android:name="com.google.android.gms.common.api.GoogleApiActivity"
360-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:288:13-79
361            android:exported="false"
361-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:289:13-37
362            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
362-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:287:13-72
363
364        <receiver
364-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:290:9-293:39
365            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
365-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:291:13-85
366            android:enabled="true"
366-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:292:13-35
367            android:exported="false" />
367-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:293:13-37
368
369        <service
369-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:294:9-297:39
370            android:name="com.google.android.gms.measurement.AppMeasurementService"
370-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:295:13-84
371            android:enabled="true"
371-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:296:13-35
372            android:exported="false" />
372-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:297:13-37
373        <service
373-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:298:9-302:39
374            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
374-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:299:13-87
375            android:enabled="true"
375-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:301:13-35
376            android:exported="false"
376-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:302:13-37
377            android:permission="android.permission.BIND_JOB_SERVICE" />
377-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:300:13-69
378
379        <meta-data
379-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:303:9-305:68
380            android:name="com.google.android.gms.version"
380-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:304:13-58
381            android:value="@integer/google_play_services_version" />
381-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:305:13-66
382
383        <service
383-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:306:9-309:45
384            android:name="androidx.room.MultiInstanceInvalidationService"
384-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:307:13-74
385            android:directBootAware="true"
385-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:309:13-43
386            android:exported="false" />
386-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:308:13-37
387        <service
387-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:310:9-313:39
388            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
388-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:311:13-117
389            android:exported="false"
389-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:313:13-37
390            android:permission="android.permission.BIND_JOB_SERVICE" />
390-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:312:13-69
391
392        <receiver
392-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:314:9-316:39
393            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
393-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:315:13-132
394            android:exported="false" />
394-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:316:13-37
395
396        <service
396-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\8dd12166401814958a84a1864c86ce1b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
397            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
397-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\8dd12166401814958a84a1864c86ce1b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
398            android:enabled="true"
398-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\8dd12166401814958a84a1864c86ce1b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
399            android:exported="false" >
399-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\8dd12166401814958a84a1864c86ce1b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
400            <meta-data
400-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\8dd12166401814958a84a1864c86ce1b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
401                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
401-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\8dd12166401814958a84a1864c86ce1b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
402                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
402-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\8dd12166401814958a84a1864c86ce1b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
403        </service>
404
405        <activity
405-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\8dd12166401814958a84a1864c86ce1b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
406            android:name="androidx.credentials.playservices.HiddenActivity"
406-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\8dd12166401814958a84a1864c86ce1b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
407            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
407-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\8dd12166401814958a84a1864c86ce1b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
408            android:enabled="true"
408-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\8dd12166401814958a84a1864c86ce1b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
409            android:exported="false"
409-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\8dd12166401814958a84a1864c86ce1b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
410            android:fitsSystemWindows="true"
410-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\8dd12166401814958a84a1864c86ce1b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
411            android:theme="@style/Theme.Hidden" >
411-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.14\transforms\8dd12166401814958a84a1864c86ce1b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
412        </activity>
413        <activity
413-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8b2512062bb9036d84302ff507f29fba\transformed\play-services-auth-20.7.0\AndroidManifest.xml:23:9-27:75
414            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
414-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8b2512062bb9036d84302ff507f29fba\transformed\play-services-auth-20.7.0\AndroidManifest.xml:24:13-93
415            android:excludeFromRecents="true"
415-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8b2512062bb9036d84302ff507f29fba\transformed\play-services-auth-20.7.0\AndroidManifest.xml:25:13-46
416            android:exported="false"
416-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8b2512062bb9036d84302ff507f29fba\transformed\play-services-auth-20.7.0\AndroidManifest.xml:26:13-37
417            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
417-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8b2512062bb9036d84302ff507f29fba\transformed\play-services-auth-20.7.0\AndroidManifest.xml:27:13-72
418        <!--
419            Service handling Google Sign-In user revocation. For apps that do not integrate with
420            Google Sign-In, this service will never be started.
421        -->
422        <service
422-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8b2512062bb9036d84302ff507f29fba\transformed\play-services-auth-20.7.0\AndroidManifest.xml:33:9-37:51
423            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
423-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8b2512062bb9036d84302ff507f29fba\transformed\play-services-auth-20.7.0\AndroidManifest.xml:34:13-89
424            android:exported="true"
424-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8b2512062bb9036d84302ff507f29fba\transformed\play-services-auth-20.7.0\AndroidManifest.xml:35:13-36
425            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
425-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8b2512062bb9036d84302ff507f29fba\transformed\play-services-auth-20.7.0\AndroidManifest.xml:36:13-107
426            android:visibleToInstantApps="true" />
426-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\8b2512062bb9036d84302ff507f29fba\transformed\play-services-auth-20.7.0\AndroidManifest.xml:37:13-48
427        <service
427-->[com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\493565d3d4690f5a6802e799ea942eae\transformed\firebase-sessions-2.1.2\AndroidManifest.xml:22:9-25:40
428            android:name="com.google.firebase.sessions.SessionLifecycleService"
428-->[com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\493565d3d4690f5a6802e799ea942eae\transformed\firebase-sessions-2.1.2\AndroidManifest.xml:23:13-80
429            android:enabled="true"
429-->[com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\493565d3d4690f5a6802e799ea942eae\transformed\firebase-sessions-2.1.2\AndroidManifest.xml:24:13-35
430            android:exported="false" />
430-->[com.google.firebase:firebase-sessions:2.1.2] C:\Users\<USER>\.gradle\caches\8.14\transforms\493565d3d4690f5a6802e799ea942eae\transformed\firebase-sessions-2.1.2\AndroidManifest.xml:25:13-37
431
432        <receiver
432-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:61:9-66:35
433            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
433-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:62:13-88
434            android:directBootAware="false"
434-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:63:13-44
435            android:enabled="true"
435-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:64:13-35
436            android:exported="false" />
436-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:65:13-37
437        <receiver
437-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:67:9-77:20
438            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
438-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:68:13-106
439            android:directBootAware="false"
439-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:69:13-44
440            android:enabled="false"
440-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:70:13-36
441            android:exported="false" >
441-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:71:13-37
442            <intent-filter>
442-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:129:13-132:29
443                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
443-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:130:17-86
443-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:130:25-84
444                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
444-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:131:17-89
444-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:131:25-87
445            </intent-filter>
446        </receiver>
447        <receiver
447-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:78:9-88:20
448            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
448-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:79:13-104
449            android:directBootAware="false"
449-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:80:13-44
450            android:enabled="false"
450-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:81:13-36
451            android:exported="false" >
451-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:82:13-37
452            <intent-filter>
452-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:139:13-142:29
453                <action android:name="android.intent.action.BATTERY_OKAY" />
453-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:140:17-76
453-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:140:25-74
454                <action android:name="android.intent.action.BATTERY_LOW" />
454-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:141:17-75
454-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:141:25-73
455            </intent-filter>
456        </receiver>
457        <receiver
457-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:89:9-99:20
458            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
458-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:90:13-104
459            android:directBootAware="false"
459-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:91:13-44
460            android:enabled="false"
460-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:92:13-36
461            android:exported="false" >
461-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:93:13-37
462            <intent-filter>
462-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:149:13-152:29
463                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
463-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:150:17-82
463-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:150:25-80
464                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
464-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:151:17-81
464-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:151:25-79
465            </intent-filter>
466        </receiver>
467        <receiver
467-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:100:9-109:20
468            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
468-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:101:13-103
469            android:directBootAware="false"
469-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:102:13-44
470            android:enabled="false"
470-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:103:13-36
471            android:exported="false" >
471-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\702ffc3917b4477cb68ec1d175701202\transformed\work-runtime-2.9.0\AndroidManifest.xml:104:13-37
472            <intent-filter>
472-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:159:13-161:29
473                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
473-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:160:17-78
473-->C:\xampp\htdocs\AppyStoreMRecharge\LicenseActivationTest\app\src\main\AndroidManifest.xml:160:25-76
474            </intent-filter>
475        </receiver>
476
477        <uses-library
477-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14\transforms\0f90a3bf636d390373b4501e1b796823\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:23:9-25:40
478            android:name="android.ext.adservices"
478-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14\transforms\0f90a3bf636d390373b4501e1b796823\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:24:13-50
479            android:required="false" />
479-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.14\transforms\0f90a3bf636d390373b4501e1b796823\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:25:13-37
480
481        <receiver
481-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c7263e1c10130702e5eff4afa7938ce6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
482            android:name="androidx.profileinstaller.ProfileInstallReceiver"
482-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c7263e1c10130702e5eff4afa7938ce6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
483            android:directBootAware="false"
483-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c7263e1c10130702e5eff4afa7938ce6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
484            android:enabled="true"
484-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c7263e1c10130702e5eff4afa7938ce6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
485            android:exported="true"
485-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c7263e1c10130702e5eff4afa7938ce6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
486            android:permission="android.permission.DUMP" >
486-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c7263e1c10130702e5eff4afa7938ce6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
487            <intent-filter>
487-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c7263e1c10130702e5eff4afa7938ce6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
488                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
488-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c7263e1c10130702e5eff4afa7938ce6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
488-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c7263e1c10130702e5eff4afa7938ce6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
489            </intent-filter>
490            <intent-filter>
490-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c7263e1c10130702e5eff4afa7938ce6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
491                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
491-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c7263e1c10130702e5eff4afa7938ce6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
491-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c7263e1c10130702e5eff4afa7938ce6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
492            </intent-filter>
493            <intent-filter>
493-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c7263e1c10130702e5eff4afa7938ce6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
494                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
494-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c7263e1c10130702e5eff4afa7938ce6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
494-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c7263e1c10130702e5eff4afa7938ce6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
495            </intent-filter>
496            <intent-filter>
496-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c7263e1c10130702e5eff4afa7938ce6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
497                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
497-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c7263e1c10130702e5eff4afa7938ce6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
497-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.14\transforms\c7263e1c10130702e5eff4afa7938ce6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
498            </intent-filter>
499        </receiver> <!-- The activities will be merged into the manifest of the hosting app. -->
500        <activity
500-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\3f6b128159a1ce82a882c0b825dba2e7\transformed\core-common-2.0.3\AndroidManifest.xml:14:9-18:65
501            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
501-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\3f6b128159a1ce82a882c0b825dba2e7\transformed\core-common-2.0.3\AndroidManifest.xml:15:13-93
502            android:exported="false"
502-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\3f6b128159a1ce82a882c0b825dba2e7\transformed\core-common-2.0.3\AndroidManifest.xml:16:13-37
503            android:stateNotNeeded="true"
503-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\3f6b128159a1ce82a882c0b825dba2e7\transformed\core-common-2.0.3\AndroidManifest.xml:17:13-42
504            android:theme="@style/Theme.PlayCore.Transparent" />
504-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.14\transforms\3f6b128159a1ce82a882c0b825dba2e7\transformed\core-common-2.0.3\AndroidManifest.xml:18:13-62
505    </application>
506
507</manifest>
