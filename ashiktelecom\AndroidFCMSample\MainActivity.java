package com.ashiktelecom.fcmsample;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.NotificationCompat;
import androidx.core.app.NotificationManagerCompat;

import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;
import android.widget.TextView;
import android.widget.Toast;

import com.google.android.gms.tasks.OnCompleteListener;
import com.google.android.gms.tasks.Task;
import com.google.firebase.messaging.FirebaseMessaging;

public class MainActivity extends AppCompatActivity {

    private static final String TAG = "MainActivity";
    private TextView tokenTextView;
    private TextView statusTextView;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        tokenTextView = findViewById(R.id.tokenTextView);
        statusTextView = findViewById(R.id.statusTextView);

        // Create notification channel for Android O and above
        createNotificationChannel();

        // Get FCM token
        getFirebaseToken();

        // Subscribe to topic
        subscribeToTopic();
    }

    private void createNotificationChannel() {
        // Create the NotificationChannel, but only on API 26+ because
        // the NotificationChannel class is new and not in the support library
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            CharSequence name = getString(R.string.channel_name);
            String description = getString(R.string.channel_description);
            int importance = NotificationManager.IMPORTANCE_DEFAULT;
            NotificationChannel channel = new NotificationChannel(
                    getString(R.string.default_notification_channel_id), name, importance);
            channel.setDescription(description);
            
            // Register the channel with the system
            NotificationManager notificationManager = getSystemService(NotificationManager.class);
            notificationManager.createNotificationChannel(channel);
            
            Log.d(TAG, "Notification channel created");
        }
    }

    private void getFirebaseToken() {
        FirebaseMessaging.getInstance().getToken()
                .addOnCompleteListener(new OnCompleteListener<String>() {
                    @Override
                    public void onComplete(@NonNull Task<String> task) {
                        if (!task.isSuccessful()) {
                            Log.w(TAG, "Fetching FCM registration token failed", task.getException());
                            tokenTextView.setText("Token: Error getting token");
                            return;
                        }

                        // Get new FCM registration token
                        String token = task.getResult();

                        // Log and display the token
                        Log.d(TAG, "FCM Token: " + token);
                        tokenTextView.setText("Token: " + token);
                    }
                });
    }

    private void subscribeToTopic() {
        // Subscribe to the topic "55555"
        FirebaseMessaging.getInstance().subscribeToTopic("55555")
                .addOnCompleteListener(new OnCompleteListener<Void>() {
                    @Override
                    public void onComplete(@NonNull Task<Void> task) {
                        String msg = "Subscribed to topic: 55555";
                        if (!task.isSuccessful()) {
                            msg = "Failed to subscribe to topic: 55555";
                            Log.w(TAG, msg);
                        } else {
                            Log.d(TAG, msg);
                        }
                        statusTextView.setText(msg);
                        Toast.makeText(MainActivity.this, msg, Toast.LENGTH_SHORT).show();
                    }
                });
    }
}
