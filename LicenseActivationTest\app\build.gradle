plugins {
    alias(libs.plugins.android.application)

    // Make sure that you have the Google services Gradle plugin
    id 'com.google.gms.google-services'

    // Add the Crashlytics Gradle plugin
    id 'com.google.firebase.crashlytics'
}

android {
    namespace 'com.appystore.mrecharge'
    compileSdk 35

    defaultConfig {
        applicationId "com.appystore.mrecharge"
        minSdk 24
        targetSdk 35
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
        coreLibraryDesugaringEnabled true
    }
}

dependencies {
    implementation libs.firebase.auth
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.1.3'

    implementation libs.appcompat
    implementation libs.material
    implementation libs.activity
    implementation libs.constraintlayout

    // Retrofit for network requests
    implementation libs.retrofit
    implementation libs.converter.gson
    implementation libs.okhttp
    implementation libs.logging.interceptor
    
    // OkHttp3 for network operations
    implementation 'com.squareup.okhttp3:okhttp:4.11.0'
    implementation 'com.squareup.okhttp3:logging-interceptor:4.11.0'
    
    // For @Keep annotation used in models
    implementation 'androidx.annotation:annotation:1.7.1'

    // Firebase and Google Play Services
    implementation platform(libs.firebase.bom)
    implementation libs.firebase.messaging
    implementation libs.play.services.base

    // WorkManager for background tasks
    implementation libs.work.runtime

    // Volley for network requests
    implementation libs.volley

    // Testing
    testImplementation libs.junit
    androidTestImplementation libs.ext.junit
    androidTestImplementation libs.espresso.core
    
    // For better logging during development
    debugImplementation 'com.facebook.stetho:stetho:1.6.0'
    debugImplementation 'com.facebook.stetho:stetho-okhttp3:1.6.0'

    // Add the dependencies for the Crashlytics and Analytics libraries
    // Import the BoM for the Firebase platform
    implementation(platform("com.google.firebase:firebase-bom:33.16.0"))
    // When using the BoM, you don't specify versions in Firebase library dependencies
    implementation("com.google.firebase:firebase-crashlytics")
    implementation("com.google.firebase:firebase-analytics")
}
