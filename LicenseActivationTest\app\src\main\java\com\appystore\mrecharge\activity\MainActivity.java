package com.appystore.mrecharge.activity;


import android.accessibilityservice.AccessibilityServiceInfo;
import android.app.ActivityManager;
import android.app.AlertDialog;
import android.app.ProgressDialog;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.SharedPreferences;
import android.net.Uri;
import android.os.AsyncTask;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.PowerManager;
import android.preference.PreferenceManager;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Log;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.accessibility.AccessibilityManager;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.CompoundButton;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.Spinner;
import android.widget.SpinnerAdapter;
import android.widget.TextView;
import android.widget.Toast;
import android.widget.ToggleButton;

import androidx.activity.EdgeToEdge;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;
import androidx.work.OneTimeWorkRequest;
import androidx.work.WorkManager;

import com.android.volley.RequestQueue;
import com.appystore.mrecharge.NetworkClient;
import com.appystore.mrecharge.R;
import com.appystore.mrecharge.WResponse;
import com.appystore.mrecharge.app.Config;
import com.appystore.mrecharge.service.USSDService;
import com.appystore.mrecharge.service.sever;
import com.appystore.mrecharge.util.Devicer;
import com.appystore.mrecharge.util.Android15CompatibilityHelper;
import com.appystore.mrecharge.util.PermissionHelper;
import com.google.android.gms.common.internal.ImagesContract;
import com.google.firebase.FirebaseApp;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import java.io.OutputStream;
import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLEncoder;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import retrofit2.Call;
import retrofit2.Callback;


public class MainActivity extends AppCompatActivity {
    private static final int IGNORE_BATTERY_OPTIMIZATION_REQUEST = 1002;
    private static final int MY_PERMISSIONS_REQUEST_READ_PHONE_STATE = 0;
    private static final int PERMISSION_REQUEST_CODE = 1;
    private static final int MAX_BUTTON_CLICKS = 7;
    private static final int BUTTON_COOLDOWN_TIME = 5 * 60 * 1000; // 5 minutes in milliseconds
    private String ACCESSIBILITY_SERVICE_NAME;
    String FinalJSonObject;
    boolean al;
    String alert;
    BroadcastReceiver br;
    ToggleButton buttonClick;
    String emid;
    boolean flag;
    private Handler handler;
    boolean isReceiverRegistered;
    private BroadcastReceiver mRegistrationBroadcastReceiver;
    OneTimeWorkRequest mRequest;
    WorkManager mWorkManager;
    EditText mpin;
    TextView myidt;
    private ProgressDialog pDialog;
    private RadioButton radioButton;
    private RadioGroup radioGroup;
    private RadioGroup radioGroup2;

    // Variables for tracking button clicks
    private int buttonClickCount = 0;
    private long lastClickResetTime = 0;
    private boolean buttonCooldownActive = false;
    private RadioGroup radioGroup3;
    private RequestQueue requestQueue;
    private Runnable runnable;
    String servname;
    String sf;
    String sim1;
    String sim2;
    Spinner simn1;
    Spinner simn2;
    SharedPreferences sp;
    EditText url;
    String xemid;
    private static final String[] distic = {"OFF", "Grameenphone", "Robi", "Banglalink", "Airtel", "Teletalk"};
    private static final String TAG = MainActivity.class.getSimpleName();
    public static int ACTION_MANAGE_OVERLAY_PERMISSION_REQUEST_CODE = 2323;
    String[] permissions = {"android.permission.SEND_SMS", "android.permission.CALL_PHONE"};
    private final int runTime = 5000;
    private int MY_PERMISSIONS_REQUEST_SMS_RECEIVE = 10;
    int PERMISSION_ALL = 1;
    String[] PERMISSIONS = {"android.permission.SEND_SMS", "android.permission.CALL_PHONE", "android.permission.RECEIVE_SMS", "android.permission.READ_SMS", "android.permission.READ_PHONE_STATE", "android.permission.RECEIVE_BOOT_COMPLETED", "android.permission.WAKE_LOCK"};

    @Override
    protected void onCreate(Bundle bundle) {

        EdgeToEdge.enable(this);
        super.onCreate(bundle);


        setContentView(R.layout.activity_main);
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.activity_main), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });

        // Initialize handler for button cooldown
        handler = new Handler();

        // Initialize click tracking
        resetButtonClickCount();

        // Initialize Firebase if not already initialized
        try {
            FirebaseApp.initializeApp(this);
            Log.d("FIREBASE", "Firebase initialized successfully");

            // Initialize Firebase Crashlytics
            FirebaseCrashlytics crashlytics = FirebaseCrashlytics.getInstance();
            crashlytics.setCrashlyticsCollectionEnabled(true);

            // Log app startup for debugging
            crashlytics.log("MainActivity onCreate started");
            crashlytics.setCustomKey("app_version", "1.0");
            crashlytics.setCustomKey("target_sdk", "35");
            crashlytics.setCustomKey("device_api_level", Build.VERSION.SDK_INT);

            Log.d("CRASHLYTICS", "Firebase Crashlytics initialized successfully");
        } catch (Exception e) {
            Log.e("FIREBASE", "Error initializing Firebase: " + e.getMessage(), e);
        }

        // Log the device ID for debugging
        String deviceId = getPrefid("myid", getApplicationContext());
        Log.d("DEVICE_INFO", "Device ID: " + deviceId);
        Log.d("DEVICE_INFO", "Device Name: " + device_name());

        // License Activation Logic
        final View licenseActivationView = findViewById(R.id.license_activation);
        final View btnSpeakContainer = findViewById(R.id.btnSpeakContainer);
        final EditText licenseKeyField = findViewById(R.id.licenseKeyField);
        final EditText pinField = findViewById(R.id.pinField);
        final Button activateButton = findViewById(R.id.activateButton);
        final EditText urlField = findViewById(R.id.licensekey);
        final EditText mpinField = findViewById(R.id.dpin);

        // Check if license is activated
        String savedLicense = getPref("licence", getApplicationContext());
        String savedPin = getPref("pin", getApplicationContext());
        boolean isActivated = !TextUtils.isEmpty(savedLicense) && !TextUtils.isEmpty(savedPin);

        if (!isActivated) {
            // Show activation layout, hide main content
            if (licenseActivationView != null) licenseActivationView.setVisibility(View.VISIBLE);
            if (btnSpeakContainer != null) btnSpeakContainer.setVisibility(View.GONE);
            if (urlField != null) urlField.setVisibility(View.GONE);
            if (mpinField != null) mpinField.setVisibility(View.GONE);
        } else {
            // Hide activation, show main
            if (licenseActivationView != null) licenseActivationView.setVisibility(View.GONE);
            if (btnSpeakContainer != null) btnSpeakContainer.setVisibility(View.VISIBLE);
            if (urlField != null) {
                urlField.setText(savedLicense);
                urlField.setVisibility(View.VISIBLE);
            }
            if (mpinField != null) {
                mpinField.setText(savedPin);
                mpinField.setVisibility(View.VISIBLE);
            }
        }

        // Activate button logic
        if (activateButton != null) {
            activateButton.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    String licenseKey = licenseKeyField != null ? licenseKeyField.getText().toString().trim() : "";
                    String pin = pinField != null ? pinField.getText().toString().trim() : "";
                    if (TextUtils.isEmpty(licenseKey) || TextUtils.isEmpty(pin)) {
                        Toast.makeText(MainActivity.this, "Please enter license key and pin", Toast.LENGTH_SHORT).show();
                        return;
                    }
                    // Simulate server request (replace with actual network call)
                    // On success:
                    // Save to SharedPreferences
                    SavePreferences("licence", licenseKey);
                    SavePreferences("pin", pin);
                    // Optionally save domain, etc. from server response
                    // Hide activation, show main
                    if (licenseActivationView != null) licenseActivationView.setVisibility(View.GONE);
                    if (btnSpeakContainer != null) btnSpeakContainer.setVisibility(View.VISIBLE);
                    if (urlField != null) {
                        urlField.setText(licenseKey);
                        urlField.setVisibility(View.VISIBLE);
                    }
                    if (mpinField != null) {
                        mpinField.setText(pin);
                        mpinField.setVisibility(View.VISIBLE);
                    }
                    Toast.makeText(MainActivity.this, "License activated!", Toast.LENGTH_SHORT).show();
                }
            });
        }

        // Check for license key mismatch on startup
        checkLicenseKeyMismatch();
        this.simn1 = (Spinner) findViewById(R.id.simaname);
        this.simn2 = (Spinner) findViewById(R.id.simbname);

        // Use the new permission helper for Android 15 compatibility
        if (!PermissionHelper.hasAllNecessaryPermissions(this)) {
            Log.d(TAG, "Missing permissions: " + PermissionHelper.getMissingPermissionsDescription(this));
            PermissionHelper.requestAllNecessaryPermissions(this);
        }
        this.sp = getSharedPreferences("pref", 0);
        Log.e("1003", this.sp.getString("token", ""));
        RadioButton radioButton = (RadioButton) findViewById(R.id.newm);
        ArrayAdapter arrayAdapter = new ArrayAdapter(this, android.R.layout.simple_spinner_item, distic);
        arrayAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        this.simn1.setAdapter((SpinnerAdapter) arrayAdapter);
        ArrayAdapter arrayAdapter2 = new ArrayAdapter(this, android.R.layout.simple_spinner_item, distic);
        arrayAdapter2.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        this.simn2.setAdapter((SpinnerAdapter) arrayAdapter2);
        this.radioGroup = (RadioGroup) findViewById(R.id.server_type);
        if (this.sp.getBoolean("gateway_on", false)) {
            radioButton.setChecked(true);
        } else {
            radioButton.setChecked(false);
        }
        // Safely find and cast views
        View bankView = findViewById(R.id.bank);
        View smsView = findViewById(R.id.sms);
        View monView = findViewById(R.id.mon);

        LinearLayout linearLayout = null;
        LinearLayout linearLayout2 = null;
        LinearLayout linearLayout3 = null;

        // Only cast to LinearLayout if the views are actually LinearLayouts
        if (bankView instanceof LinearLayout) {
            linearLayout = (LinearLayout) bankView;
        }

        if (smsView instanceof LinearLayout) {
            linearLayout2 = (LinearLayout) smsView;
        }

        if (monView instanceof LinearLayout) {
            linearLayout3 = (LinearLayout) monView;
        }
        ToggleButton toggleButton = (ToggleButton) findViewById(R.id.auto);
        final ToggleButton toggleButton2 = (ToggleButton) findViewById(R.id.bettery);
        this.buttonClick = (ToggleButton) findViewById(R.id.sav);
        ToggleButton toggleButton3 = (ToggleButton) findViewById(R.id.sms_service);
        getWindow().setSoftInputMode(2);
        this.url = (EditText) findViewById(R.id.licensekey);
        this.mpin = (EditText) findViewById(R.id.dpin);
        this.myidt = (TextView) findViewById(R.id.myid);
        if (TextUtils.isEmpty(getPrefid("myid", getApplicationContext()))) {
            SavePreferences("myid", randomCode());
        }
        this.myidt.setText("Device ID: " + getPrefid("myid", getApplicationContext()));
        String pref = getPref("licence", getApplicationContext());
        if (!TextUtils.isEmpty(pref)) {
            this.url.setText(pref);
        }
        ToggleButton toggleButton4 = (ToggleButton) findViewById(R.id.next);
        this.mpin.setText(getPrefx("pin", getApplicationContext()));
        SharedPreferences sharedPreferences = getSharedPreferences("serv", 0);
        final int i = sharedPreferences.getInt("sima_service", 0);
        sharedPreferences.getInt("simb_service", 0);
        this.simn1.setSelection(sharedPreferences.getInt("simap", 0));
        this.simn2.setSelection(sharedPreferences.getInt("simbp", 0));
        int i2 = sharedPreferences.getInt("stop", 0);
        if (sharedPreferences.getInt("page", 0) == 1) {
            toggleButton4.setChecked(true);
        } else {
            toggleButton4.setChecked(false);
        }
        if (i2 == 1 && isMyServiceRunning(sever.class)) {
            this.buttonClick.setChecked(true);
        } else {
            this.buttonClick.setChecked(false);
        }
        if (sharedPreferences.getInt("sms_service", 0) == 1) {
            toggleButton3.setChecked(true);
        } else {
            toggleButton3.setChecked(false);
        }
        if (isAccessibilitySettingsOn(this)) {
            toggleButton.setChecked(true);
        } else {
            toggleButton.setChecked(false);
        }
        final PowerManager powerManager = (PowerManager) getSystemService(Context.POWER_SERVICE);
        if (Build.VERSION.SDK_INT >= 23) {
            if (powerManager != null && !powerManager.isIgnoringBatteryOptimizations(getPackageName())) {
                askIgnoreOptimization();
                toggleButton2.setChecked(false);
            } else {
                toggleButton2.setChecked(true);
            }
        }
        this.simn1.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override // android.widget.AdapterView.OnItemSelectedListener
            public void onNothingSelected(AdapterView<?> adapterView) {
            }

            @Override // android.widget.AdapterView.OnItemSelectedListener
            public void onItemSelected(AdapterView<?> adapterView, View view, int i3, long j) {
                int i4 = i;
                if (i3 == 0) {
                    MainActivity.this.servname = "NO";
                }
                if (i3 == 1) {
                    MainActivity.this.servname = "GP";
                }
                if (i3 == 2) {
                    MainActivity.this.servname = "RB";
                }
                if (i3 == 3) {
                    MainActivity.this.servname = "BL";
                }
                if (i3 == 4) {
                    MainActivity.this.servname = "AT";
                }
                if (i3 == 5) {
                    MainActivity.this.servname = "TT";
                }
                SharedPreferences.Editor edit = MainActivity.this.getSharedPreferences("serv", 0).edit();
                edit.putInt("simap", i3);
                edit.commit();
                MainActivity mainActivity = MainActivity.this;
                mainActivity.SavePreferences("m1", mainActivity.servname);
            }
        });
        this.simn2.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override // android.widget.AdapterView.OnItemSelectedListener
            public void onNothingSelected(AdapterView<?> adapterView) {
            }

            @Override // android.widget.AdapterView.OnItemSelectedListener
            public void onItemSelected(AdapterView<?> adapterView, View view, int i3, long j) {
                int i4 = i;
                if (i3 == 0) {
                    MainActivity.this.servname = "NO";
                }
                if (i3 == 1) {
                    MainActivity.this.servname = "GP";
                }
                if (i3 == 2) {
                    MainActivity.this.servname = "RB";
                }
                if (i3 == 3) {
                    MainActivity.this.servname = "BL";
                }
                if (i3 == 4) {
                    MainActivity.this.servname = "AT";
                }
                if (i3 == 5) {
                    MainActivity.this.servname = "TT";
                }
                SharedPreferences.Editor edit = MainActivity.this.getSharedPreferences("serv", 0).edit();
                edit.putInt("simbp", i3);
                edit.commit();
                MainActivity mainActivity = MainActivity.this;
                mainActivity.SavePreferences("m2", mainActivity.servname);
            }
        });
        toggleButton.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton compoundButton, boolean z) {
                MainActivity.this.startActivityForResult(new Intent("android.settings.ACCESSIBILITY_SETTINGS"), 0);
            }
        });
        toggleButton2.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton compoundButton, boolean z) {
                if (Build.VERSION.SDK_INT >= 23) {
                    PowerManager powerManager2 = powerManager;
                    if (powerManager2 != null && !powerManager2.isIgnoringBatteryOptimizations(MainActivity.this.getPackageName())) {
                        MainActivity.this.askIgnoreOptimization();
                        toggleButton2.setChecked(true);
                    } else {
                        toggleButton2.setChecked(false);
                    }
                }
            }
        });
        toggleButton3.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton compoundButton, boolean z) {
                int i3;
                if (z) {
                    i3 = 1;
                    MainActivity.this.servname = "SM";
                } else {
                    MainActivity.this.servname = "OFF";
                    i3 = 0;
                }
                SharedPreferences.Editor edit = MainActivity.this.getSharedPreferences("serv", 0).edit();
                edit.putInt("sms_service", i3);
                edit.commit();
                MainActivity mainActivity = MainActivity.this;
                mainActivity.SavePreferences("m3", mainActivity.servname);
            }
        });
        toggleButton4.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton compoundButton, boolean z) {
                SharedPreferences.Editor edit = MainActivity.this.getSharedPreferences("serv", 0).edit();
                edit.putInt("page", z ? 1 : 0);
                edit.commit();
            }
        });
        this.buttonClick.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton compoundButton, boolean z) {
                try {
                    // Log the button click for debugging
                    FirebaseCrashlytics.getInstance().log("ToggleButton clicked: " + z);

                    // Check if activity is still valid (Android 15 compatibility)
                    if (!Android15CompatibilityHelper.isActivitySafe(MainActivity.this)) {
                        Log.w(TAG, "Activity is not safe, ignoring button click");
                        return;
                    }

                    // Log the button click for debugging
                    Android15CompatibilityHelper.logToCrashlytics("ToggleButton clicked: " + z);

                    // Check if button is in cooldown
                    if (buttonCooldownActive) {
                        // Don't allow toggling during cooldown
                        compoundButton.setChecked(!z);
                        Android15CompatibilityHelper.showSafeToast(MainActivity.this,
                            "Button is disabled for 5 minutes due to excessive clicks", Toast.LENGTH_LONG);
                        return;
                    }

                    // Increment click counter
                    incrementButtonClickCount();

                    // Check if we need to show warning or disable button
                    if (buttonClickCount == 3) {
                        // Show warning alert at 3 clicks
                        showWarningAlert();
                    } else if (buttonClickCount >= MAX_BUTTON_CLICKS) {
                        // Show danger alert and disable button at 4 clicks
                        showDangerAlertAndDisableButton();
                        compoundButton.setChecked(!z);
                        return;
                    }

                    // Continue with normal button functionality
                    if (MainActivity.this.radioGroup != null && MainActivity.this.radioGroup.getCheckedRadioButtonId() == R.id.olmd) {
                        MainActivity.this.getSharedPreferences("pref", 0).edit().putBoolean("gateway_on", false).apply();
                    } else {
                        MainActivity.this.getSharedPreferences("pref", 0).edit().putBoolean("gateway_on", true).apply();
                    }

                    if (z) {
                        // Validate inputs with null checks
                        if (MainActivity.this.mpin == null || MainActivity.this.mpin.length() < 4) {
                            MainActivity.this.buttonClick.setChecked(false);
                            Android15CompatibilityHelper.showSafeToast(MainActivity.this,
                                "Please Enter pin", Toast.LENGTH_LONG);
                            return;
                        } else if (MainActivity.this.url == null || MainActivity.this.url.length() < 4) {
                            MainActivity.this.buttonClick.setChecked(false);
                            Android15CompatibilityHelper.showSafeToast(MainActivity.this,
                                "Please enter License key", Toast.LENGTH_LONG);
                            return;
                        } else {
                            // Perform validation check in background thread
                            MainActivity.this.check(0);
                            return;
                        }
                    }

                    // Turn off service
                    MainActivity mainActivity = MainActivity.this;
                    mainActivity.servname = "OFF";
                    mainActivity.getSharedPreferences("pref", 0).edit().putBoolean("server_on", false).apply();
                    SharedPreferences.Editor edit = MainActivity.this.getSharedPreferences("serv", 0).edit();
                    edit.putInt("stop", 0);
                    edit.apply(); // Use apply() instead of commit() for better performance
                    MainActivity.this.stopService();

                } catch (Exception e) {
                    // Catch any unexpected exceptions to prevent crashes
                    Log.e(TAG, "Error in ToggleButton click handler: " + e.getMessage(), e);
                    Android15CompatibilityHelper.recordException(e);

                    // Reset button state on error
                    if (compoundButton != null) {
                        compoundButton.setChecked(false);
                    }
                    Android15CompatibilityHelper.showSafeToast(MainActivity.this,
                        "An error occurred. Please try again.", Toast.LENGTH_LONG);
                }
            }
        });
        // Set click listeners only if the views are LinearLayouts
        if (linearLayout3 != null) {
            linearLayout3.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    MainActivity.this.startActivity(new Intent(MainActivity.this, (Class<?>) Monitoring.class));
                }
            });
        } else {
            // Alternative way to handle clicks for monView if it's not a LinearLayout
            monView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    MainActivity.this.startActivity(new Intent(MainActivity.this, (Class<?>) Monitoring.class));
                }
            });
        }

        if (linearLayout2 != null) {
            linearLayout2.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    MainActivity.this.startActivity(new Intent(MainActivity.this, com.appystore.mrecharge.activity.Settings.class));
                }
            });
        } else {
            // Alternative way to handle clicks for smsView if it's not a LinearLayout
            smsView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    MainActivity.this.startActivity(new Intent(MainActivity.this, com.appystore.mrecharge.activity.Settings.class));
                }
            });
        }

        if (linearLayout != null) {
            linearLayout.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    MainActivity.this.startActivity(new Intent(MainActivity.this, (Class<?>) intsetting.class));
                }
            });
        } else {
            // Alternative way to handle clicks for bankView if it's not a LinearLayout
            bankView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    MainActivity.this.startActivity(new Intent(MainActivity.this, (Class<?>) intsetting.class));
                }
            });
        }

        // Add a long click listener to test server connection
        this.buttonClick.setOnLongClickListener(new View.OnLongClickListener() {
            @Override
            public boolean onLongClick(View v) {
                // Show confirmation dialog before testing
                AlertDialog.Builder builder = new AlertDialog.Builder(MainActivity.this);
                builder.setTitle("Test API Connection");
                builder.setMessage("Do you want to test the connection to the API server?");
                builder.setPositiveButton("Yes", new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        // Test server connection
                        testServerConnection();
                    }
                });
                builder.setNegativeButton("No", null);
                builder.show();
                return true;
            }
        });
    }

    @Override
    public void onRequestPermissionsResult(int i, String[] strArr, int[] iArr) {
        super.onRequestPermissionsResult(i, strArr, iArr);
    }

    @Override
    public void onActivityResult(int i, int i2, Intent intent) {
        super.onActivityResult(i, i2, intent);
        if (i != ACTION_MANAGE_OVERLAY_PERMISSION_REQUEST_CODE || Build.VERSION.SDK_INT < 23 || android.provider.Settings.canDrawOverlays(getApplicationContext())) {
            return;
        }
        RequestPermission();
    }

    @Override // android.app.Activity
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.menu_main, menu);
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem menuItem) {
        int itemId = menuItem.getItemId();
        if (itemId == R.id.inter) {
            startActivity(new Intent(this, (Class<?>) intsetting.class));
            return true;
        }
        if (itemId == R.id.forward) {
            startActivity(new Intent(this, com.appystore.mrecharge.activity.Settings.class));
            return true;
        }
        return super.onOptionsItemSelected(menuItem);
    }

    String randomCode() {
        UUID randomUUID = UUID.randomUUID();
        long leastSignificantBits = randomUUID.getLeastSignificantBits();
        long mostSignificantBits = randomUUID.getMostSignificantBits();

        long combined = Math.abs(mostSignificantBits ^ (mostSignificantBits >> 33))
                + Math.abs(leastSignificantBits ^ (leastSignificantBits >> 33));

        String code = String.format("%010d", combined);
        return code.substring(code.length() - 10);
    }

    private void RequestPermission() {
        if (Build.VERSION.SDK_INT >= 23) {
            startActivityForResult(new Intent("android.settings.action.MANAGE_OVERLAY_PERMISSION", Uri.parse("package:" + getPackageName())), ACTION_MANAGE_OVERLAY_PERMISSION_REQUEST_CODE);
        }
    }

    public void SavePreferences(String str, String str2) {
        SharedPreferences.Editor edit = PreferenceManager.getDefaultSharedPreferences(getApplicationContext()).edit();
        edit.putString(str, str2);
        edit.commit();
    }

    private boolean isMyServiceRunning(Class<?> cls) {
        Iterator<ActivityManager.RunningServiceInfo> it = ((ActivityManager) getSystemService("activity")).getRunningServices(Integer.MAX_VALUE).iterator();
        while (it.hasNext()) {
            if (cls.getName().equals(it.next().service.getClassName())) {
                return true;
            }
        }
        return false;
    }

    public static boolean hasPermissions(Context context, String... strArr) {
        if (context == null || strArr == null) {
            return true;
        }
        for (String str : strArr) {
            if (ActivityCompat.checkSelfPermission(context, str) != 0) {
                return false;
            }
        }
        return true;
    }

    public void startService() {
        Intent intent = new Intent(this, (Class<?>) sever.class);
        intent.putExtra("inputExtra", "Flexiload is running..");
        intent.putExtra("serv", "on");
        ContextCompat.startForegroundService(this, intent);
    }

    public void stopService() {
        if (isMyServiceRunning(sever.class)) {
            Intent intent = new Intent(this, (Class<?>) sever.class);
            intent.putExtra("inputExtra", "Flexiload is running..");
            intent.putExtra("serv", "off");
            ContextCompat.startForegroundService(this, intent);
        }
    }

    public static boolean isAccessibilityEnabled(Context context, String str) {
        Iterator<AccessibilityServiceInfo> it = ((AccessibilityManager) context.getSystemService("accessibility")).getEnabledAccessibilityServiceList(-1).iterator();
        while (it.hasNext()) {
            if (str.equals(it.next().getId())) {
                return true;
            }
        }
        return false;
    }

    public static String getPrefid(String str, Context context) {
        return PreferenceManager.getDefaultSharedPreferences(context).getString(str, null);
    }

    public static String getPref(String str, Context context) {
        return PreferenceManager.getDefaultSharedPreferences(context).getString(str, "");
    }

    public static String getPrefx(String str, Context context) {
        return PreferenceManager.getDefaultSharedPreferences(context).getString(str, "1234");
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void check(int i) {
        this.pDialog = new ProgressDialog(this);
        if (i == 0) {
            this.pDialog.setMessage("Checking...");
        } else {
            this.pDialog.setMessage("Registering Device...");
        }
        this.pDialog.setIndeterminate(false);
        this.pDialog.setCancelable(false);
        this.pDialog.show();

        // Get the API URL from shared preferences or use default
        String apiUrl = getApiUrl();

        // Log the API URL being used
        Log.d("API_CONFIG", "Using API endpoint: " + apiUrl);

        // Validate URL format
        try {
            URL url = new URL(apiUrl);
            // URL is valid, proceed
            Log.e("API_REQUEST", "Sending request to: " + apiUrl);
        } catch (MalformedURLException e) {
            Log.e("API_ERROR", "Invalid URL format: " + e.getMessage());
            this.pDialog.dismiss();
            Toast.makeText(getBaseContext(), "Invalid server URL format", Toast.LENGTH_LONG).show();
            this.buttonClick.setChecked(false);
            return;
        }

        // Try a different approach with a direct HTTP connection
        new Thread(new Runnable() {
            @Override
            public void run() {
                HttpURLConnection connection = null;
                try {
                    // Create connection
                    URL url = new URL(apiUrl);
                    connection = (HttpURLConnection) url.openConnection();
                    connection.setRequestMethod("POST");
                    connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
                    connection.setRequestProperty("User-Agent", "AppyStoreMRecharge Android App");
                    connection.setRequestProperty("Accept", "*/*");

                    // Enable input/output
                    connection.setDoInput(true);
                    connection.setDoOutput(true);

                    // Create parameters
                    String licenseKey = MainActivity.this.url.getText().toString().trim();
                    String pin = MainActivity.this.mpin.getText().toString().trim();
                    String deviceId = getPrefid("myid", getApplicationContext());
                    String deviceInfo = device_name();

                    String postData = "license_key=" + URLEncoder.encode(licenseKey, "UTF-8") +
                            "&pin=" + URLEncoder.encode(pin, "UTF-8") +
                            "&type=server" +
                            "&pkg=platinum" +
                            "&version=20" +
                            "&device_id=" + URLEncoder.encode(deviceId, "UTF-8") +
                            "&device_info=" + URLEncoder.encode(deviceInfo, "UTF-8") +
                            "&timestamp=" + System.currentTimeMillis();

                    Log.d("API_PARAMS", "Sending params: " + postData);

                    // Send post data
                    try (DataOutputStream wr = new DataOutputStream(connection.getOutputStream())) {
                        wr.writeBytes(postData);
                        wr.flush();
                    }

                    // Get response
                    int responseCode = connection.getResponseCode();
                    Log.d("API_RESPONSE", "Response code: " + responseCode);

                    if (responseCode == HttpURLConnection.HTTP_OK) {
                        // Read response
                        StringBuilder response = new StringBuilder();
                        try (BufferedReader in = new BufferedReader(
                                new InputStreamReader(connection.getInputStream()))) {
                            String line;
                            while ((line = in.readLine()) != null) {
                                response.append(line);
                            }
                        }

                        final String responseData = response.toString();
                        Log.d("API_RESPONSE", "Response: " + responseData);

                        // Update UI on main thread
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                MainActivity.this.pDialog.dismiss();
                                MainActivity.this.FinalJSonObject = responseData;
                                new Checkstatus(getApplicationContext()).execute();
                            }
                        });
                    } else {
                        // Handle error
                        StringBuilder errorResponse = new StringBuilder();
                        try (BufferedReader in = new BufferedReader(
                                new InputStreamReader(connection.getErrorStream()))) {
                            String line;
                            while ((line = in.readLine()) != null) {
                                errorResponse.append(line);
                            }
                        }

                        final String errorData = errorResponse.toString();
                        Log.e("API_ERROR", "Error response: " + errorData);
                        Log.e("API_ERROR", "Response code: " + responseCode);

                        // Log headers
                        Map<String, List<String>> headers = connection.getHeaderFields();
                        for (Map.Entry<String, List<String>> entry : headers.entrySet()) {
                            Log.e("API_ERROR", "Header: " + entry.getKey() + " = " + entry.getValue());
                        }

                        // Update UI on main thread
                        final int finalResponseCode = responseCode;
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                MainActivity.this.pDialog.dismiss();
                                Toast.makeText(
                                        MainActivity.this.getBaseContext(),
                                        "Error code: " + finalResponseCode,
                                        Toast.LENGTH_LONG
                                ).show();
                                MainActivity.this.buttonClick.setChecked(false);
                            }
                        });
                    }
                } catch (final Exception e) {
                    Log.e("API_ERROR", "Exception: " + e.getMessage(), e);

                    // Update UI on main thread
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            MainActivity.this.pDialog.dismiss();
                            Toast.makeText(
                                    MainActivity.this.getBaseContext(),
                                    "Error: " + e.getMessage(),
                                    Toast.LENGTH_LONG
                            ).show();
                            MainActivity.this.buttonClick.setChecked(false);
                        }
                    });
                } finally {
                    if (connection != null) {
                        connection.disconnect();
                    }
                }
            }
        }).start();

        // No additional configuration needed - the HTTP request is handled in the thread
    }

    private class Checkstatus extends AsyncTask<Void, Void, Boolean> {
        private Context context;
        private String errorMessage = "";
        private JSONObject responseData = null;

        public Checkstatus(Context context) {
            this.context = context;
        }

        @Override
        protected void onPreExecute() {
            super.onPreExecute();
            Log.d("API_PROCESS", "Starting to process API response");
        }

        @Override
        protected Boolean doInBackground(Void... params) {
            try {
                // Check if we have a response
                if (MainActivity.this.FinalJSonObject == null) {
                    errorMessage = "No response received";
                    return false;
                }

                try {
                    // Parse the JSON response
                    Log.d("API_PROCESS", "Parsing JSON response: " + MainActivity.this.FinalJSonObject);
                    JSONArray jsonArray = new JSONArray(MainActivity.this.FinalJSonObject);

                    // Process the first object in the array
                    if (jsonArray.length() > 0) {
                        responseData = jsonArray.getJSONObject(0);

                        // Check status and version
                        int status = responseData.getInt("status");
                        int version = responseData.getInt("version");

                        Log.d("API_PROCESS", "Response status: " + status + ", version: " + version);

                        if (status == 1 && version == 1) {
                            // Valid response with correct version - approved
                            return true;
                        } else if (status == 2 && version == 1) {
                            // Status 2 means pending approval
                            if (responseData.has("message")) {
                                errorMessage = responseData.getString("message");
                            } else {
                                errorMessage = "Your device is pending approval by the administrator. Please try again later.";
                            }

                            // Save log ID if available for future reference
                            if (responseData.has("log_id")) {
                                String logId = responseData.getString("log_id");
                                MainActivity.this.SavePreferences("pending_approval_log_id", logId);
                            }

                            return false;
                        } else {
                            // Invalid status or version
                            if (version == 0) {
                                errorMessage = "Please update app!";
                            } else {
                                // Get the error message from the response if available
                                if (responseData.has("message")) {
                                    errorMessage = responseData.getString("message");
                                } else {
                                    errorMessage = "License validation failed";
                                }
                            }
                            return false;
                        }
                    } else {
                        errorMessage = "Empty response from server";
                        return false;
                    }
                } catch (JSONException e) {
                    Log.e("API_PROCESS", "JSON parsing error", e);
                    errorMessage = "Invalid response format";
                    return false;
                }
            } catch (Exception e) {
                Log.e("API_PROCESS", "Error processing response", e);
                errorMessage = "Error processing response: " + e.getMessage();
                return false;
            }
        }

        @Override
        protected void onPostExecute(Boolean success) {
            super.onPostExecute(success);

            if (success && responseData != null) {
                try {
                    // Save protocol (http/https)
                    if (responseData.getInt("sec") == 1) {
                        MainActivity.this.SavePreferences("sec", "https");
                    } else {
                        MainActivity.this.SavePreferences("sec", "http");
                    }

                    // Save credentials and settings
                    MainActivity.this.SavePreferences("pin", MainActivity.this.mpin.getText().toString());
                    MainActivity.this.SavePreferences(ImagesContract.URL, responseData.getString("domain"));

                    // Get the app license key (entered by user)
                    String appLicenseKey = MainActivity.this.url.getText().toString();
                    MainActivity.this.SavePreferences("licence", appLicenseKey);

                    // Check if server license key is in the response
                    if (responseData.has("server_license_key")) {
                        String serverLicenseKey = responseData.getString("server_license_key");

                        // Save the server license key
                        MainActivity.this.SavePreferences("server_license_key", serverLicenseKey);

                        // Check if the app license key matches the server license key
                        if (!appLicenseKey.equals(serverLicenseKey)) {
                            // Log the mismatch
                            Log.w("LICENSE_MISMATCH", "App license key (" + appLicenseKey +
                                    ") doesn't match server license key (" + serverLicenseKey + ")");

                            // Show license key mismatch warning
                            MainActivity.this.showLicenseKeyMismatchWarning(appLicenseKey, serverLicenseKey);
                        }
                    }

                    // Save additional data if available
                    if (responseData.has(ImagesContract.URL)) {
                        MainActivity.this.SavePreferences("curl", responseData.getString(ImagesContract.URL));
                    }

                    // Set service to active
                    SharedPreferences.Editor edit = MainActivity.this.getSharedPreferences("serv", 0).edit();
                    edit.putInt("stop", 1);
                    edit.apply();

                    // Get domain from response
                    String domain = responseData.getString("domain");

                    // Log domain information
                    Log.d("API_PROCESS", "Domain: " + domain);

                    // Note: Firebase messaging subscription removed to prevent crashes

                    // Generate a unique secret for this session
                    MainActivity.this.SavePreferences("secret", UUID.randomUUID().toString());

                    // Set flag for successful activation
                    MainActivity.this.al = true;

                    // Update UI and start service
                    MainActivity.this.getSharedPreferences("pref", 0).edit().putBoolean("server_on", true).apply();
                    MainActivity.this.startService();

                    // Show success message
                    Toast.makeText(
                            MainActivity.this.getBaseContext(),
                            "App active & running",
                            Toast.LENGTH_LONG
                    ).show();

                    // Register device with server
                    MainActivity.this.Device_reg();

                    // Log success
                    Log.d("API_PROCESS", "Successfully activated app");

                } catch (JSONException e) {
                    Log.e("API_PROCESS", "Error processing successful response", e);
                    MainActivity.this.buttonClick.setChecked(false);
                    Toast.makeText(
                            MainActivity.this.getBaseContext(),
                            "Error processing response",
                            Toast.LENGTH_LONG
                    ).show();
                }
            } else {
                // Handle failure
                MainActivity.this.buttonClick.setChecked(false);
                Toast.makeText(
                        MainActivity.this.getBaseContext(),
                        errorMessage,
                        Toast.LENGTH_LONG
                ).show();
                MainActivity.this.flag = true;

                Log.e("API_PROCESS", "Activation failed: " + errorMessage + " | License key: " + MainActivity.this.url.getText().toString() + " | PIN: " + MainActivity.this.mpin.getText().toString());
            }
        }
    }
    public void Device_reg() {
        // Create server URL from preferences
        String domain = getPref(ImagesContract.URL, getApplicationContext());

        // Fix the domain path - remove /mitload if present and use correct base path
        if (domain.contains("/mitload")) {
            domain = domain.replace("/mitload", "");
        }

        String baseUrl = getPref("sec", getApplicationContext()) + "://" + domain;

        // Log the base URL for debugging
        Log.d("DeviceReg", "Base URL: " + baseUrl);

        // Get user information from preferences
        String m1 = getPref("m1", getApplicationContext());
        String m2 = getPref("m2", getApplicationContext());
        String m3 = getPref("m3", getApplicationContext());
        String m4 = getPref("m4", getApplicationContext());
        String m5 = getPref("m5", getApplicationContext());
        String m6 = getPref("m6", getApplicationContext());
        String m7 = getPref("m7", getApplicationContext());
        String m8 = getPref("m8", getApplicationContext());
        String myId = getPrefid("myid", getApplicationContext());
        String deviceName = device_name();

        // Log the device ID and name for debugging
        Log.d("DeviceReg", "Device ID: " + myId);
        Log.d("DeviceReg", "Device Name: " + deviceName);
        Log.d("DeviceReg", "M1-M8 values: " + m1 + ", " + m2 + ", " + m3 + ", " + m4 + ", " + m5 + ", " + m6 + ", " + m7 + ", " + m8);

        try {
            // Create Retrofit client
            Devicer apiService = NetworkClient
                    .getRetrofitClient(baseUrl)
                    .create(Devicer.class);

            // Make API call with callback
            apiService.Devicer(m1, m2, m3, m4, m5, m6, m7, m8, myId, deviceName)
                    .enqueue(new Callback<WResponse>() {
                        @Override
                        public void onResponse(Call<WResponse> call, retrofit2.Response<WResponse> response) {
                            if (response.isSuccessful()) {
                                // Successfully received response
                                WResponse wResponse = response.body();
                                if (wResponse != null) {
                                    Integer status = wResponse.getstatus();
                                    if (status != null && status == 1) {
                                        // Registration successful
                                        Log.d("DeviceReg", "Registration successful with status: " + status);
                                        Toast.makeText(getApplicationContext(), "Device registered successfully", Toast.LENGTH_SHORT).show();
                                    } else {
                                        // Registration failed with error status
                                        Log.e("DeviceReg", "Registration failed with status: " + (status != null ? status : "null"));
                                        Toast.makeText(getApplicationContext(), "Device registration failed", Toast.LENGTH_SHORT).show();
                                    }
                                } else {
                                    // Empty response body
                                    Log.e("DeviceReg", "Empty response body");
                                    Toast.makeText(getApplicationContext(), "Empty response from server", Toast.LENGTH_SHORT).show();
                                }
                            } else {
                                // HTTP error
                                Log.e("DeviceReg", "HTTP error: " + response.code() + " - " + response.message());
                                try {
                                    String errorBody = response.errorBody() != null ? response.errorBody().string() : "No error body";
                                    Log.e("DeviceReg", "Error body: " + errorBody);
                                } catch (Exception e) {
                                    Log.e("DeviceReg", "Error reading error body: " + e.getMessage());
                                }
                                Toast.makeText(getApplicationContext(), "Server error: " + response.code(), Toast.LENGTH_SHORT).show();

                                // Try alternative method if HTTP error
                                tryAlternativeRegistration(baseUrl, m1, m2, m3, m4, m5, m6, m7, m8, myId, deviceName);
                            }
                        }

                        @Override
                        public void onFailure(Call<WResponse> call, Throwable t) {
                            // Network or other error
                            Log.e("DeviceReg", "Registration failed: " + t.getMessage(), t);
                            Toast.makeText(getApplicationContext(), "Connection error: " + t.getMessage(), Toast.LENGTH_SHORT).show();

                            // Try alternative method if Retrofit fails
                            tryAlternativeRegistration(baseUrl, m1, m2, m3, m4, m5, m6, m7, m8, myId, deviceName);
                        }
                    });
        } catch (Exception e) {
            // Handle any exceptions during setup
            Log.e("DeviceReg", "Exception setting up API call: " + e.getMessage(), e);
            Toast.makeText(getApplicationContext(), "Error: " + e.getMessage(), Toast.LENGTH_SHORT).show();

            // Try alternative method if Retrofit setup fails
            tryAlternativeRegistration(baseUrl, m1, m2, m3, m4, m5, m6, m7, m8, myId, deviceName);
        }
    }
    /**
     * Try an alternative method to register the device using HttpURLConnection
     */
    private void tryAlternativeRegistration(final String baseUrl, final String m1, final String m2,
                                            final String m3, final String m4, final String m5,
                                            final String m6, final String m7, final String m8,
                                            final String deviceId, final String deviceName) {
        new Thread(new Runnable() {
            @Override
            public void run() {
                HttpURLConnection connection = null;
                try {
                    // Create URL for the device endpoint
                    URL url = new URL(baseUrl + "/index.php/Modemcon/device");
                    Log.d("DeviceReg", "Alternative method URL: " + url.toString());

                    // Open connection
                    connection = (HttpURLConnection) url.openConnection();
                    connection.setRequestMethod("POST");
                    connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
                    connection.setDoOutput(true);

                    // Create POST data
                    String postData = "m1=" + URLEncoder.encode(m1, "UTF-8") +
                            "&m2=" + URLEncoder.encode(m2, "UTF-8") +
                            "&m3=" + URLEncoder.encode(m3, "UTF-8") +
                            "&m4=" + URLEncoder.encode(m4, "UTF-8") +
                            "&m5=" + URLEncoder.encode(m5, "UTF-8") +
                            "&m6=" + URLEncoder.encode(m6, "UTF-8") +
                            "&m7=" + URLEncoder.encode(m7, "UTF-8") +
                            "&m8=" + URLEncoder.encode(m8, "UTF-8") +
                            "&device=" + URLEncoder.encode(deviceId, "UTF-8") +
                            "&name=" + URLEncoder.encode(deviceName, "UTF-8");

                    Log.d("DeviceReg", "Alternative method POST data: " + postData);

                    // Send POST data
                    try (DataOutputStream wr = new DataOutputStream(connection.getOutputStream())) {
                        wr.writeBytes(postData);
                        wr.flush();
                    }

                    // Get response
                    int responseCode = connection.getResponseCode();
                    Log.d("DeviceReg", "Alternative method response code: " + responseCode);

                    if (responseCode == HttpURLConnection.HTTP_OK) {
                        // Read response
                        StringBuilder response = new StringBuilder();
                        try (BufferedReader in = new BufferedReader(
                                new InputStreamReader(connection.getInputStream()))) {
                            String line;
                            while ((line = in.readLine()) != null) {
                                response.append(line);
                            }
                        }

                        final String responseData = response.toString();
                        Log.d("DeviceReg", "Alternative method response: " + responseData);

                        // Update UI on main thread
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                Toast.makeText(getApplicationContext(),
                                        "Device registered successfully (alternative method)",
                                        Toast.LENGTH_SHORT).show();
                            }
                        });
                    } else {
                        // Handle error
                        StringBuilder errorResponse = new StringBuilder();
                        try (BufferedReader in = new BufferedReader(
                                new InputStreamReader(connection.getErrorStream()))) {
                            String line;
                            while ((line = in.readLine()) != null) {
                                errorResponse.append(line);
                            }
                        }

                        final String errorData = errorResponse.toString();
                        Log.e("DeviceReg", "Alternative method error: " + errorData);

                        // Update UI on main thread
                        final int finalResponseCode = responseCode;
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                Toast.makeText(getApplicationContext(),
                                        "Registration failed (alternative method): " + finalResponseCode,
                                        Toast.LENGTH_SHORT).show();
                            }
                        });
                    }
                } catch (final Exception e) {
                    Log.e("DeviceReg", "Alternative method exception: " + e.getMessage(), e);

                    // Update UI on main thread
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            Toast.makeText(getApplicationContext(),
                                    "Registration error: " + e.getMessage(),
                                    Toast.LENGTH_SHORT).show();
                        }
                    });
                } finally {
                    if (connection != null) {
                        connection.disconnect();
                    }
                }
            }
        }).start();
    }

    /* JADX INFO: Access modifiers changed from: private */
//    public void Device_reg() {
//        // সার্ভার URL তৈরি করা (sec ও URL প্রেফারেন্স থেকে)
//        String baseUrl = getPref("sec", getApplicationContext()) + "://" + getPref(ImagesContract.URL, getApplicationContext());
//
//        // ইউজারের ইনফরমেশনগুলো প্রেফারেন্স থেকে নেওয়া
//        String m1 = getPref("m1", getApplicationContext());
//        String m2 = getPref("m2", getApplicationContext());
//        String m3 = getPref("m3", getApplicationContext());
//        String m4 = getPref("m4", getApplicationContext());
//        String m5 = getPref("m5", getApplicationContext());
//        String m6 = getPref("m6", getApplicationContext());
//        String m7 = getPref("m7", getApplicationContext());
//        String m8 = getPref("m8", getApplicationContext());
//        String myId = getPrefid("myid", getApplicationContext());
//        String deviceName = device_name();
//
//        // Retrofit ক্লায়েন্ট তৈরি
//        Devicer apiService = NetworkClient
//                .getRetrofitClient(baseUrl)
//                .create(Devicer.class);
//
//        // API কল এবং Callback সেট করা
//        apiService.Devicer(m1, m2, m3, m4, m5, m6, m7, m8, myId, deviceName)
//                .enqueue(new Callback() {
//                    @Override
//                    public void onResponse(Call call, retrofit2.Response response) {
//                        // সার্ভার থেকে সফল রেসপন্স পেলে এখানে কোড লিখতে পারেন
//                        Log.d("DeviceReg", "Registration success: " + response.message());
//                    }
//
//                    @Override
//                    public void onFailure(Call call, Throwable t) {
//                        // কোনো সমস্যা হলে এখানে হ্যান্ডেল করতে পারেন
//                        Log.e("DeviceReg", "Registration failed: " + t.getMessage());
//                    }
//                });
//    }


    private boolean isAccessibilitySettingsOn(Context context) {
        int i;
        String string;
        String str = getPackageName() + "/" + USSDService.class.getCanonicalName();
        try {
            i = Settings.Secure.getInt(context.getApplicationContext().getContentResolver(), "accessibility_enabled");
        } catch (Settings.SettingNotFoundException unused) {
            i = 0;
        }
        TextUtils.SimpleStringSplitter simpleStringSplitter = new TextUtils.SimpleStringSplitter(':');
        if (i == 1 && (string = Settings.Secure.getString(context.getApplicationContext().getContentResolver(), "enabled_accessibility_services")) != null) {
            simpleStringSplitter.setString(string);
            while (simpleStringSplitter.hasNext()) {
                if (simpleStringSplitter.next().equalsIgnoreCase(str)) {
                    return true;
                }
            }
        }
        return false;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void askIgnoreOptimization() {
        if (Build.VERSION.SDK_INT >= 23) {
            Intent intent = new Intent("android.settings.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS");
            intent.setData(Uri.parse("package:" + getPackageName()));
            startActivityForResult(intent, 1002);
        }
    }

    public static boolean hasper(Context context, String... strArr) {
        if (context == null || strArr == null) {
            return true;
        }
        for (String str : strArr) {
            if (ActivityCompat.checkSelfPermission(context, str) != 0) {
                return false;
            }
        }
        return true;
    }

    @Override // androidx.fragment.app.FragmentActivity, android.app.Activity
    protected void onResume() {
        super.onResume();
        // Check if the app is already activated
        if (this.al) {
            // App is already activated, start the service
            startService();
        } else {
            // Check if we have a pending approval
            String pendingLogId = getPref("pending_approval_log_id", getApplicationContext());
            if (pendingLogId != null && !pendingLogId.isEmpty()) {
                // We have a pending approval, check its status
                checkApprovalStatus();
            }
        }
    }

    /**
     * Check if a pending approval has been approved
     */
    private void checkApprovalStatus() {
        String pendingLogId = getPref("pending_approval_log_id", getApplicationContext());
        if (pendingLogId == null || pendingLogId.isEmpty()) {
            return; // No pending approval
        }

        // Show progress dialog
        final ProgressDialog progressDialog = new ProgressDialog(this);
        progressDialog.setMessage("Checking approval status...");
        progressDialog.setIndeterminate(true);
        progressDialog.setCancelable(false);
        progressDialog.show();

        // Get the API URL
        final String apiUrl = getApiUrl();

        // Create a new thread for the network request
        new Thread(new Runnable() {
            @Override
            public void run() {
                HttpURLConnection connection = null;
                try {
                    // Create URL
                    URL url = new URL(apiUrl);

                    // Open connection
                    connection = (HttpURLConnection) url.openConnection();
                    connection.setRequestMethod("POST");
                    connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
                    connection.setDoOutput(true);

                    // Prepare POST data
                    String postData = "check_approval=1" +
                            "&log_id=" + pendingLogId +
                            "&license_key=" + MainActivity.this.url.getText().toString() +
                            "&device_id=" + Settings.Secure.getString(MainActivity.this.getContentResolver(), "android_id");

                    // Send POST data
                    try (OutputStream os = connection.getOutputStream()) {
                        os.write(postData.getBytes("UTF-8"));
                    }

                    // Get response
                    int responseCode = connection.getResponseCode();

                    if (responseCode == HttpURLConnection.HTTP_OK) {
                        // Read response
                        StringBuilder response = new StringBuilder();
                        try (BufferedReader in = new BufferedReader(
                                new InputStreamReader(connection.getInputStream()))) {
                            String line;
                            while ((line = in.readLine()) != null) {
                                response.append(line);
                            }
                        }

                        final String responseData = response.toString();
                        Log.d("APPROVAL_CHECK", "Response: " + responseData);

                        // Process response on UI thread
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                progressDialog.dismiss();

                                try {
                                    JSONArray jsonArray = new JSONArray(responseData);
                                    if (jsonArray.length() > 0) {
                                        JSONObject jsonObject = jsonArray.getJSONObject(0);
                                        int status = jsonObject.getInt("status");

                                        if (status == 1) {
                                            // Approval granted, retry activation
                                            Toast.makeText(MainActivity.this,
                                                    "Your device has been approved! Activating...",
                                                    Toast.LENGTH_LONG).show();

                                            // Clear pending approval ID
                                            SavePreferences("pending_approval_log_id", "");

                                            // Trigger activation
                                            check(0);
                                        } else if (status == 2) {
                                            // Still pending
                                            Toast.makeText(MainActivity.this,
                                                    "Your device is still pending approval. Please try again later.",
                                                    Toast.LENGTH_LONG).show();
                                        } else {
                                            // Rejected or other error
                                            String message = jsonObject.has("message") ?
                                                    jsonObject.getString("message") :
                                                    "Your approval request was not approved.";

                                            Toast.makeText(MainActivity.this, message, Toast.LENGTH_LONG).show();

                                            // Clear pending approval ID
                                            SavePreferences("pending_approval_log_id", "");
                                        }
                                    }
                                } catch (Exception e) {
                                    Log.e("APPROVAL_CHECK", "Error processing response", e);
                                    Toast.makeText(MainActivity.this,
                                            "Error checking approval status: " + e.getMessage(),
                                            Toast.LENGTH_LONG).show();
                                }
                            }
                        });
                    } else {
                        // Handle error
                        final int finalResponseCode = responseCode;
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                progressDialog.dismiss();
                                Toast.makeText(MainActivity.this,
                                        "Error checking approval status. Code: " + finalResponseCode,
                                        Toast.LENGTH_LONG).show();
                            }
                        });
                    }
                } catch (final Exception e) {
                    Log.e("APPROVAL_CHECK", "Exception: " + e.getMessage(), e);

                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            progressDialog.dismiss();
                            Toast.makeText(MainActivity.this,
                                    "Error: " + e.getMessage(),
                                    Toast.LENGTH_LONG).show();
                        }
                    });
                } finally {
                    if (connection != null) {
                        connection.disconnect();
                    }
                }
            }
        }).start();
    }

    @Override // androidx.fragment.app.FragmentActivity, android.app.Activity
    protected void onPause() {
        super.onPause();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        // Clean up resources if needed
    }

    private String device_name() {
        return Build.MANUFACTURER + " " + Build.MODEL + " V:" + Build.VERSION.RELEASE;
    }

    /**
     * Get the API URL from shared preferences or use the default URL
     * @return The API URL to use for authentication
     */
    private String getApiUrl() {
        // Get default API URL from Config class
        String defaultApiUrl = Config.getDefaultLicenseApiUrl();

        // Try to get the API URL from shared preferences
        String savedApiUrl = PreferenceManager.getDefaultSharedPreferences(getApplicationContext())
                .getString(Config.PREF_KEY_API_URL, null);

        // If no saved URL or it's empty, use the default and save it
        if (savedApiUrl == null || savedApiUrl.isEmpty()) {
            // Save the default URL for future use
            SavePreferences(Config.PREF_KEY_API_URL, defaultApiUrl);
            return defaultApiUrl;
        }

        return savedApiUrl;
    }

    /**
     * Set a new API URL to use for authentication
     * @param newApiUrl The new API URL to use
     */
    private void setApiUrl(String newApiUrl) {
        if (newApiUrl != null && !newApiUrl.isEmpty()) {
            // Validate URL format
            try {
                URL url = new URL(newApiUrl);
                // URL is valid, save it
                SavePreferences("api_url", newApiUrl);
                Log.d("API_CONFIG", "API URL updated to: " + newApiUrl);
            } catch (MalformedURLException e) {
                Log.e("API_ERROR", "Invalid API URL format: " + e.getMessage());
            }
        }
    }

    /**
     * Reset the button click counter
     */
    private void resetButtonClickCount() {
        buttonClickCount = 0;
        lastClickResetTime = System.currentTimeMillis();
        buttonCooldownActive = false;
    }

    /**
     * Show a Toast message safely (Android 15 compatible)
     * @param message The message to display
     */
    private void showSafeToast(String message) {
        Android15CompatibilityHelper.showSafeToast(this, message, Toast.LENGTH_LONG);
    }

    /**
     * Increment the button click counter
     * Reset the counter if it's been more than 10 seconds since the last click
     */
    private void incrementButtonClickCount() {
        long currentTime = System.currentTimeMillis();

        // Reset counter if it's been more than 10 seconds since the last click
        if (currentTime - lastClickResetTime > 10000) {
            buttonClickCount = 0;
        }

        buttonClickCount++;
        lastClickResetTime = currentTime;

        Log.d("BUTTON_CLICKS", "Button click count: " + buttonClickCount);
    }

    /**
     * Show a warning alert after 3 clicks (Android 15 compatible)
     */
    private void showWarningAlert() {
        Android15CompatibilityHelper.showSafeDialog(this,
            "Warning",
            "You are clicking too frequently. One more click will disable the button for 5 minutes.",
            "OK",
            null);
    }

    /**
     * Show a danger alert and disable the button for 5 minutes after 4 clicks (Android 15 compatible)
     */
    private void showDangerAlertAndDisableButton() {
        Android15CompatibilityHelper.showSafeDialog(this,
            "Button Disabled",
            "Due to excessive clicking, this button has been disabled for 5 minutes.",
            "OK",
            null);

        // Set cooldown flag
        buttonCooldownActive = true;

        // Schedule re-enabling the button after 5 minutes
        if (handler != null) {
            handler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    buttonCooldownActive = false;
                    resetButtonClickCount();
                    Android15CompatibilityHelper.showSafeToast(MainActivity.this,
                        "Button has been re-enabled", Toast.LENGTH_SHORT);
                }
            }, BUTTON_COOLDOWN_TIME);
        }
    }

    /**
     * Check if there's a mismatch between the saved app license key and server license key
     */
    private void checkLicenseKeyMismatch() {
        try {
            // Get the saved license keys
            String appLicenseKey = getPref("licence", getApplicationContext());
            String serverLicenseKey = getPref("server_license_key", getApplicationContext());

            // Check if both keys exist and are not null
            if (appLicenseKey != null && serverLicenseKey != null &&
                    !appLicenseKey.isEmpty() && !serverLicenseKey.isEmpty() &&
                    !appLicenseKey.equals(serverLicenseKey)) {

                Log.w("LICENSE_MISMATCH", "Saved app license key (" + appLicenseKey +
                        ") doesn't match saved server license key (" + serverLicenseKey + ")");

                // Show the mismatch warning
                showLicenseKeyMismatchWarning(appLicenseKey, serverLicenseKey);
            }
        } catch (Exception e) {
            // Log the error but don't crash the app
            Log.e("LICENSE_MISMATCH", "Error checking license key mismatch: " + e.getMessage(), e);
        }
    }

    /**
     * Show a warning dialog when the app license key doesn't match the server license key
     * @param appLicenseKey The license key entered by the user
     * @param serverLicenseKey The license key returned by the server
     */
    private void showLicenseKeyMismatchWarning(final String appLicenseKey, final String serverLicenseKey) {
        // Safety check to prevent null pointer exceptions
        if (appLicenseKey == null || serverLicenseKey == null) {
            Log.e("LICENSE_MISMATCH", "Cannot show warning dialog - license keys are null");
            return;
        }

        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                AlertDialog.Builder builder = null;
                try {
                    builder = new AlertDialog.Builder(MainActivity.this);
                    builder.setTitle("License Key Mismatch Warning");

                    // Create a message with both license keys
                    String message = "The license key you entered doesn't match the server license key.\n\n" +
                            "Your license key: " + appLicenseKey + "\n" +
                            "Server license key: " + serverLicenseKey + "\n\n" +
                            "This may cause issues with app functionality. Would you like to update your license key?";

                    builder.setMessage(message);

                    // Add buttons
                    builder.setPositiveButton("Update License Key", new DialogInterface.OnClickListener() {
                        @Override
                        public void onClick(DialogInterface dialog, int which) {
                            // Update the license key field with the server license key
                            if (url != null) {
                                url.setText(serverLicenseKey);
                            }
                            SavePreferences("licence", serverLicenseKey);

                            Toast.makeText(getBaseContext(), "License key updated", Toast.LENGTH_SHORT).show();
                        }
                    });
                } catch (Exception e) {
                    Log.e("LICENSE_MISMATCH", "Error showing license key mismatch dialog: " + e.getMessage(), e);
                    return; // Exit if we couldn't create the dialog
                }

                // Continue only if builder was successfully created
                if (builder != null) {
                    try {
                        builder.setNegativeButton("Keep My Key", new DialogInterface.OnClickListener() {
                            @Override
                            public void onClick(DialogInterface dialog, int which) {
                                // User wants to keep their key, just show a toast
                                Toast.makeText(getBaseContext(),
                                        "Using your license key. Some features may not work correctly.",
                                        Toast.LENGTH_LONG).show();
                            }
                        });

                        // Show the dialog
                        AlertDialog dialog = builder.create();
                        dialog.show();
                    } catch (Exception e) {
                        Log.e("LICENSE_MISMATCH", "Error showing dialog: " + e.getMessage(), e);
                    }
                }
            }
        });
    }

    /**
     * Test if the API endpoint is reachable with a real POST request
     * This helps diagnose connection issues with the actual API endpoint
     */
    private void testServerConnection() {
        // Show a progress dialog
        final ProgressDialog testDialog = new ProgressDialog(this);
        testDialog.setMessage("Testing server connection...");
        testDialog.setIndeterminate(true);
        testDialog.setCancelable(false);
        testDialog.show();

        new Thread(new Runnable() {
            @Override
            public void run() {
                HttpURLConnection connection = null;
                try {
                    // Create a real POST request to the API endpoint
                    URL url = new URL(getApiUrl());
                    connection = (HttpURLConnection) url.openConnection();
                    connection.setRequestMethod("POST");
                    connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
                    connection.setRequestProperty("User-Agent", "AppyStoreMRecharge Android App");
                    connection.setRequestProperty("Accept", "*/*");
                    connection.setConnectTimeout(10000);
                    connection.setReadTimeout(10000);

                    // Enable input/output
                    connection.setDoInput(true);
                    connection.setDoOutput(true);

                    // Create test parameters
                    String postData = "license_key=test-license" +
                            "&pin=1234" +
                            "&type=server" +
                            "&pkg=platinum" +
                            "&version=20" +
                            "&device_id=test-device" +
                            "&device_info=Test Device" +
                            "&timestamp=" + System.currentTimeMillis();

                    Log.d("SERVER_TEST", "Sending test params: " + postData);

                    // Send post data
                    try (DataOutputStream wr = new DataOutputStream(connection.getOutputStream())) {
                        wr.writeBytes(postData);
                        wr.flush();
                    }

                    // Get response
                    int responseCode = connection.getResponseCode();
                    Log.d("SERVER_TEST", "Response code: " + responseCode);

                    // Log headers
                    Map<String, List<String>> headers = connection.getHeaderFields();
                    for (Map.Entry<String, List<String>> entry : headers.entrySet()) {
                        if (entry.getKey() != null) {
                            Log.d("SERVER_TEST", "Header: " + entry.getKey() + " = " + entry.getValue());
                        }
                    }

                    // Read response data
                    StringBuilder responseData = new StringBuilder();
                    try (BufferedReader reader = new BufferedReader(
                            new InputStreamReader(
                                    responseCode >= 400 ? connection.getErrorStream() : connection.getInputStream()))) {
                        String line;
                        while ((line = reader.readLine()) != null) {
                            responseData.append(line);
                        }
                    }

                    Log.d("SERVER_TEST", "Response data: " + responseData.toString());

                    final String result;
                    if (responseCode == HttpURLConnection.HTTP_OK) {
                        result = "API test: SUCCESS";
                    } else {
                        result = "API test: FAILED (Code: " + responseCode + ")";
                    }

                    final String responseContent = responseData.toString();

                    // Update UI on main thread
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            testDialog.dismiss();

                            // Create and show a detailed dialog with the test results
                            AlertDialog.Builder builder = new AlertDialog.Builder(MainActivity.this);
                            builder.setTitle("API Connection Test");

                            String message = result + "\n\n";
                            if (!responseContent.isEmpty()) {
                                message += "Response: " + responseContent;
                            }

                            builder.setMessage(message);
                            builder.setPositiveButton("OK", null);
                            builder.show();
                        }
                    });

                } catch (final Exception e) {
                    Log.e("SERVER_TEST", "Exception: " + e.getMessage(), e);

                    // Update UI on main thread
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            testDialog.dismiss();

                            // Create and show a detailed dialog with the error
                            AlertDialog.Builder builder = new AlertDialog.Builder(MainActivity.this);
                            builder.setTitle("API Connection Test Failed");
                            builder.setMessage("Error: " + e.getMessage());
                            builder.setPositiveButton("OK", null);
                            builder.show();
                        }
                    });
                } finally {
                    if (connection != null) {
                        connection.disconnect();
                    }
                }
            }
        }).start();
    }
}



