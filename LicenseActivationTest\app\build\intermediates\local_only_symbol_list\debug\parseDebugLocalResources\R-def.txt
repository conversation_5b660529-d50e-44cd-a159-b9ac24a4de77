R_DEF: Internal format may change without notice
local
color black
color colorPrimary
color done
color failed
color ic_launcher_background
color waiting
color white
dimen activity_horizontal_margin
drawable appystoremrecharge
drawable bg
drawable bgg
drawable button_background
drawable card_background
drawable footer_background
drawable gradient_background
drawable ic_baseline_add_to_queue_24
drawable ic_home_black_24dp
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable ic_settings_black_24dp
drawable ic_settings_cell_black_24dp
drawable ic_stat_name
drawable input_background
drawable modern_button_background
drawable modern_edit_text_background
drawable modern_status_background
drawable modern_toggle_off
drawable modern_toggle_on
drawable modern_toggle_selector
drawable nav_item_background
drawable rightorder
drawable rounded_corner
drawable rounded_cornerss
drawable save
drawable select
drawable toggle_off
drawable toggle_on
drawable toggle_selector
id activateButton
id activity_main
id at
id auto
id bank
id bettery
id bill
id bkash
id bks
id bl
id blb
id blt
id btnSpeakContainer
id countdownText
id dc
id dialog_button
id domainInfoText
id domainLoginButton
id domainLoginSection
id dpin
id expirationProgressBar
id expirationSection
id expirationText
id forward
id gp
id gpt
id home
id imageView
id inter
id itemLayout
id licenseKeyField
id license_activation
id licensekey
id main
id mon
id myid
id newm
id next
id ngs
id nogad
id olmd
id pinField
id recycleView
id rks
id robi
id rocket
id sav
id save
id savedata
id server_type
id sima_rocketj
id sima_rocketm
id simaname
id simbc
id simbname
id simsms
id sms
id sms_service
id status
id statusText
id time
id trys
id tt
id ttt
id tvPrice
id upay
layout activity_main
layout config
layout custom_dialog
layout forward
layout item_todo
layout license_activation
layout listmain
layout settingsd
menu bottom_navigation_menu
menu menu_main
mipmap ic_launcher
raw notification
string accessibility_service_description
string app_name
string com.google.firebase.crashlytics.mapping_file_id
string gcm_defaultSenderId
string google_api_key
string google_app_id
string google_crash_reporting_api_key
string google_storage_bucket
string project_id
style Base.Theme.AppyStoreMRecharge
style Theme.AppyStoreMRecharge
xml backup_rules
xml data_extraction_rules
xml network_security_config
xml ussd_service
